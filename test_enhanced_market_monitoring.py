#!/usr/bin/env python3
"""
Test script for Enhanced Options Market Monitoring Agent

This script tests the enhanced market monitoring agent with all new modules
and verifies that the integration works correctly.
"""

import asyncio
import sys
import logging
from pathlib import Path

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent))

from agents.enhanced_options_market_monitoring_agent import EnhancedOptionsMarketMonitoringAgent


async def test_enhanced_agent():
    """Test the enhanced market monitoring agent"""
    print("🚀 Testing Enhanced Options Market Monitoring Agent")
    print("=" * 60)
    
    # Create agent instance
    agent = EnhancedOptionsMarketMonitoringAgent()
    
    try:
        # Test initialization
        print("📋 Testing initialization...")
        success = await agent.initialize()
        
        if success:
            print("✅ Initialization successful!")
            
            # Test configuration management
            print("\n🔧 Testing configuration management...")
            config = agent.config_manager.get_config()
            if config:
                print(f"✅ Configuration loaded: {len(config.underlying_symbols)} symbols, {len(config.timeframes)} timeframes")
            else:
                print("❌ Configuration not loaded")
            
            # Test timeframe management
            print("\n⏰ Testing timeframe management...")
            active_timeframes = agent.timeframe_manager.get_active_timeframes()
            print(f"✅ Active timeframes: {active_timeframes}")
            
            # Test file manager
            print("\n📁 Testing file manager...")
            file_metrics = agent.file_manager.get_metrics()
            print(f"✅ File manager initialized: {file_metrics}")
            
            # Test data validator
            print("\n🔍 Testing data validator...")
            validation_summary = agent.data_validator.get_validation_summary(hours=1)
            print(f"✅ Data validator ready: {validation_summary}")
            
            # Test anomaly detector
            print("\n🚨 Testing anomaly detector...")
            anomaly_summary = agent.anomaly_detector.get_anomaly_summary(hours=1)
            print(f"✅ Anomaly detector ready: {anomaly_summary}")
            
            # Test market regime analyzer
            print("\n📊 Testing market regime analyzer...")
            regime_summary = agent.regime_analyzer.get_regime_summary('NIFTY', hours=1)
            print(f"✅ Market regime analyzer ready: {regime_summary}")
            
            # Test error handler
            print("\n⚠️ Testing error handler...")
            error_summary = agent.error_handler.get_error_summary(hours=1)
            print(f"✅ Error handler ready: {error_summary}")
            
            # Test structured logger
            print("\n📝 Testing structured logger...")
            log_stats = agent.logger.get_log_statistics()
            print(f"✅ Structured logger ready: {log_stats}")
            
            # Test a short monitoring cycle
            print("\n🔄 Testing short monitoring cycle (10 seconds)...")
            
            # Start monitoring for a short time
            monitoring_task = asyncio.create_task(agent.start())
            
            # Let it run for 10 seconds
            await asyncio.sleep(10)
            
            # Stop the agent
            agent.is_running = False
            
            try:
                await asyncio.wait_for(monitoring_task, timeout=5.0)
            except asyncio.TimeoutError:
                monitoring_task.cancel()
            
            print("✅ Short monitoring cycle completed")
            
            # Check final statistics
            print("\n📈 Final Statistics:")
            print(f"   Cycles completed: {agent.cycle_count}")
            print(f"   Errors encountered: {agent.error_count}")
            print(f"   Active timeframes: {len(agent.timeframe_manager.get_active_timeframes())}")
            print(f"   Anomalies detected: {len(agent.anomaly_logs)}")
            print(f"   Alerts generated: {len(agent.alerts_cache)}")
            
            # Test cleanup
            print("\n🧹 Testing cleanup...")
            await agent.cleanup()
            print("✅ Cleanup completed")
            
            print("\n🎉 All tests passed! Enhanced Market Monitoring Agent is working correctly.")
            
        else:
            print("❌ Initialization failed!")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


async def test_individual_modules():
    """Test individual modules separately"""
    print("\n🔧 Testing Individual Modules")
    print("=" * 40)
    
    try:
        # Test ConfigManager
        print("1. Testing ConfigManager...")
        from agents.market_monitoring.config_manager import ConfigManager
        
        config_manager = await ConfigManager.get_instance("config/options_market_monitoring_config.yaml")
        config = await config_manager.load_config()
        print(f"   ✅ Config loaded: {len(config.underlying_symbols)} symbols")
        
        # Test AsyncFileManager
        print("2. Testing AsyncFileManager...")
        from agents.market_monitoring.async_file_manager import AsyncFileManager
        
        file_manager = AsyncFileManager()
        metrics = file_manager.get_metrics()
        print(f"   ✅ File manager ready: {metrics}")
        await file_manager.cleanup()
        
        # Test TimeframeManager
        print("3. Testing TimeframeManager...")
        from agents.market_monitoring.timeframe_manager import TimeframeManager
        
        tf_manager = TimeframeManager()
        await tf_manager.sync_with_config(['1min', '5min', '15min'])
        active_tfs = tf_manager.get_active_timeframes()
        print(f"   ✅ Timeframes active: {active_tfs}")
        
        # Test AnomalyDetector
        print("4. Testing AnomalyDetector...")
        from agents.market_monitoring.anomaly_detector import AnomalyDetector
        
        anomaly_detector = AnomalyDetector()
        summary = anomaly_detector.get_anomaly_summary()
        print(f"   ✅ Anomaly detector ready: {summary}")
        
        # Test MarketRegimeAnalyzer
        print("5. Testing MarketRegimeAnalyzer...")
        from agents.market_monitoring.market_regime_analyzer import MarketRegimeAnalyzer
        
        regime_analyzer = MarketRegimeAnalyzer()
        summary = regime_analyzer.get_regime_summary('NIFTY')
        print(f"   ✅ Regime analyzer ready: {summary}")
        
        # Test ErrorHandler
        print("6. Testing ErrorHandler...")
        from agents.market_monitoring.error_handler import ErrorHandler
        
        error_handler = ErrorHandler()
        summary = error_handler.get_error_summary()
        print(f"   ✅ Error handler ready: {summary}")
        
        # Test StructuredLogger
        print("7. Testing StructuredLogger...")
        from agents.market_monitoring.structured_logger import StructuredLogger
        
        logger = StructuredLogger("test_logger")
        logger.info("Test log message")
        stats = logger.get_log_statistics()
        print(f"   ✅ Structured logger ready: {stats}")
        
        # Test DataValidator
        print("8. Testing DataValidator...")
        from agents.market_monitoring.data_validator import DataValidator
        
        data_validator = DataValidator()
        summary = data_validator.get_validation_summary()
        print(f"   ✅ Data validator ready: {summary}")
        
        print("\n✅ All individual modules tested successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Module test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function"""
    print("🧪 Enhanced Market Monitoring Agent Test Suite")
    print("=" * 80)
    
    # Test individual modules first
    modules_ok = await test_individual_modules()
    
    if modules_ok:
        # Test integrated agent
        agent_ok = await test_enhanced_agent()
        
        if agent_ok:
            print("\n🎊 ALL TESTS PASSED! 🎊")
            print("The Enhanced Market Monitoring Agent is ready for production use.")
        else:
            print("\n❌ Agent integration tests failed.")
            sys.exit(1)
    else:
        print("\n❌ Module tests failed.")
        sys.exit(1)


if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Run tests
    asyncio.run(main())
