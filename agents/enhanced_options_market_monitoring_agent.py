#!/usr/bin/env python3
"""
Enhanced Options Market Monitoring Agent - Real-time Multi-Timeframe Market Surveillance

Enhanced version with modular architecture and advanced features:
- Centralized configuration management with schema validation
- Asynchronous file I/O optimization with thread pool executors
- Dynamic timeframe management with runtime configuration
- Enhanced anomaly detection with ML models and statistical methods
- Refined market regime analysis with multiple indicators
- Comprehensive error handling with circuit breakers and retry mechanisms
- Structured logging with JSON output and contextual information
- Data validation with schema checking and quality metrics
"""

import asyncio
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import json

# Enhanced market monitoring modules
from agents.market_monitoring import (
    ConfigManager,
    AsyncFileManager,
    TimeframeManager,
    AnomalyDetector,
    MarketRegimeAnalyzer,
    ErrorHandler,
    StructuredLogger,
    DataValidator
)

# Legacy imports for backward compatibility
from agents.polars_technical_indicators_manager import PolarsTechnicalIndicatorsManager
from utils.api_rate_limiter import api_rate_limiter


class EnhancedOptionsMarketMonitoringAgent:
    """
    Enhanced Options Market Monitoring Agent with modular architecture.
    
    Features:
    - Modular design with specialized components
    - Advanced error handling and resilience
    - Comprehensive data validation
    - Enhanced anomaly detection
    - Sophisticated market regime analysis
    - Structured logging and monitoring
    - Dynamic configuration management
    - Asynchronous file operations
    """
    
    def __init__(self, config_path: str = "config/options_market_monitoring_config.yaml"):
        # Initialize structured logger first
        self.logger = StructuredLogger(
            name="enhanced_market_monitoring",
            log_file="logs/enhanced_market_monitoring.log"
        )
        
        # Initialize core components
        self.config_manager = None
        self.file_manager = AsyncFileManager(max_workers=4)
        self.timeframe_manager = TimeframeManager()
        self.anomaly_detector = AnomalyDetector()
        self.regime_analyzer = MarketRegimeAnalyzer()
        self.error_handler = ErrorHandler()
        self.data_validator = DataValidator()
        
        # Legacy components for backward compatibility
        self.indicators_manager = PolarsTechnicalIndicatorsManager()
        
        # State management
        self.is_running = False
        self.config_path = Path(config_path)
        self.market_data_cache = {}
        self.last_update = {}
        self.alerts_cache = []
        self.anomaly_logs = []
        
        # Performance tracking
        self.cycle_count = 0
        self.error_count = 0
        self.last_heartbeat = datetime.now()
        
        self.logger.info("Enhanced Options Market Monitoring Agent initialized")
    
    async def initialize(self, **kwargs) -> bool:
        """Initialize the enhanced agent with all components"""
        try:
            with self.logger.context(operation="initialization"):
                # Initialize configuration manager
                self.config_manager = await ConfigManager.get_instance(self.config_path)
                config = await self.config_manager.load_config()
                
                self.logger.info("Configuration loaded successfully", 
                               extra_data={'config_path': str(self.config_path)})
                
                # Initialize timeframe manager with config
                await self.timeframe_manager.sync_with_config(config.timeframes)
                
                # Setup timeframe change callback
                self.timeframe_manager.add_change_callback(self._on_timeframe_change)
                
                # Initialize paths
                self.live_path = Path("data/live")
                self.alerts_path = Path("data/alerts")
                self.alerts_path.mkdir(parents=True, exist_ok=True)
                
                # Clean up old data files
                await self._cleanup_old_data()
                
                # Initialize technical indicators with historical data
                await self._initialize_technical_indicators()
                
                # Initialize data structures
                self._initialize_data_structures()
                
                self.logger.info("Enhanced Market Monitoring Agent initialized successfully")
                return True
                
        except Exception as e:
            self.error_handler.record_error(e, {'operation': 'initialization'})
            self.logger.error("Failed to initialize agent", exception=e)
            return False
    
    async def start(self, **kwargs) -> bool:
        """Start the enhanced multi-timeframe market monitoring agent"""
        try:
            with self.logger.context(operation="start"):
                self.logger.info("Starting Enhanced Options Market Monitoring Agent...")
                self.is_running = True
                
                # Get active timeframes from manager
                active_timeframes = self.timeframe_manager.get_active_timeframes()
                
                # Start monitoring tasks for each active timeframe
                monitoring_tasks = []
                
                for timeframe in active_timeframes:
                    monitoring_tasks.extend([
                        self._monitor_timeframe_data_enhanced(timeframe),
                        self._detect_timeframe_regime_enhanced(timeframe),
                    ])
                
                # Add enhanced monitoring tasks
                monitoring_tasks.extend([
                    self._generate_enhanced_alerts(),
                    self._detect_enhanced_anomalies(),
                    self._generate_enhanced_summary(),
                    self._send_enhanced_heartbeat(),
                    self._monitor_system_health(),
                ])
                
                await asyncio.gather(*monitoring_tasks)
                return True
                
        except Exception as e:
            self.error_handler.record_error(e, {'operation': 'start'})
            self.logger.error("Failed to start agent", exception=e)
            return False
    
    async def _monitor_timeframe_data_enhanced(self, timeframe: str):
        """Enhanced timeframe data monitoring with error handling and validation"""
        circuit_breaker_name = f"timeframe_monitoring_{timeframe}"
        
        while self.is_running:
            try:
                with self.logger.context(timeframe=timeframe, operation="data_monitoring"):
                    # Get monitoring interval from config
                    interval = self.config_manager.get_monitoring_interval(timeframe)
                    
                    # Load and validate data with error handling
                    await self.error_handler.execute_with_retry(
                        self._load_and_validate_timeframe_data,
                        timeframe,
                        circuit_breaker_name=circuit_breaker_name
                    )
                    
                    # Analyze data with enhanced methods
                    await self._analyze_timeframe_data_enhanced(timeframe)
                    
                    self.logger.debug(f"Monitoring {timeframe} data completed")
                    await asyncio.sleep(interval)
                    
            except Exception as e:
                self.error_count += 1
                self.error_handler.record_error(e, {
                    'timeframe': timeframe,
                    'operation': 'timeframe_monitoring'
                })
                self.logger.error(f"{timeframe} data monitoring failed", exception=e)
                await asyncio.sleep(60)  # Wait before retry
    
    async def _load_and_validate_timeframe_data(self, timeframe: str):
        """Load and validate timeframe data using enhanced file manager and validator"""
        try:
            with self.logger.performance_tracking(f"load_data_{timeframe}"):
                timeframe_path = self.live_path / timeframe
                current_market_data = {}
                
                if not timeframe_path.exists():
                    self.logger.warning(f"Live data directory does not exist: {timeframe_path}")
                    return
                
                config = self.config_manager.get_config()
                files_loaded = 0
                
                for underlying in config.underlying_symbols:
                    # Find latest files using async file manager
                    pattern = f"{underlying}_{timeframe}_*.parquet"
                    files = await self.file_manager.find_latest_files_async(
                        timeframe_path, pattern, max_files=1
                    )
                    
                    if files:
                        latest_file = files[0]
                        
                        # Load data asynchronously
                        data = await self.file_manager.read_parquet_async(
                            latest_file, 
                            use_cache=True,
                            max_age_hours=2
                        )
                        
                        if data is not None and data.height > 0:
                            # Validate data quality
                            validation_result = await self.data_validator.validate_dataframe(
                                data, 'ohlc', str(latest_file)
                            )
                            
                            if validation_result.is_valid:
                                current_market_data[underlying] = data
                                files_loaded += 1
                                
                                self.logger.log_market_data(
                                    f"Loaded and validated {underlying} {timeframe}",
                                    underlying=underlying,
                                    timeframe=timeframe,
                                    extra_data={
                                        'rows': data.height,
                                        'file': latest_file.name,
                                        'quality_score': validation_result.metrics.overall_score
                                    }
                                )
                            else:
                                self.logger.warning(
                                    f"Data validation failed for {underlying} {timeframe}",
                                    extra_data={
                                        'file': latest_file.name,
                                        'issues': len(validation_result.issues),
                                        'critical_issues': len(validation_result.get_issues_by_severity('CRITICAL'))
                                    }
                                )
                
                # Update cache
                self.market_data_cache[timeframe] = current_market_data
                self.last_update[timeframe] = datetime.now()
                
                if files_loaded > 0:
                    self.logger.debug(f"Successfully loaded {files_loaded} files for {timeframe}")
                
        except Exception as e:
            self.logger.error(f"Failed to load {timeframe} data", exception=e)
            raise
    
    async def _analyze_timeframe_data_enhanced(self, timeframe: str):
        """Enhanced data analysis with anomaly detection and regime analysis"""
        try:
            if timeframe not in self.market_data_cache or not self.market_data_cache[timeframe]:
                return
            
            config = self.config_manager.get_config()
            
            for underlying in config.underlying_symbols:
                if underlying not in self.market_data_cache[timeframe]:
                    continue
                
                data = self.market_data_cache[timeframe][underlying]
                if data is None or data.height == 0:
                    continue
                
                with self.logger.context(underlying=underlying, timeframe=timeframe):
                    # Enhanced anomaly detection
                    cross_timeframe_data = {
                        tf: self.market_data_cache.get(tf, {}).get(underlying)
                        for tf in self.timeframe_manager.get_active_timeframes()
                        if tf != timeframe
                    }
                    
                    anomalies = await self.anomaly_detector.detect_anomalies(
                        data, underlying, timeframe, cross_timeframe_data
                    )
                    
                    if anomalies:
                        self.anomaly_logs.extend(anomalies)
                        for anomaly in anomalies:
                            self.logger.warning(
                                f"Anomaly detected: {anomaly.description}",
                                extra_data=anomaly.to_dict()
                            )
                    
                    # Enhanced market regime analysis
                    regime_state = await self.regime_analyzer.analyze_market_regime(
                        data, underlying, timeframe
                    )
                    
                    if regime_state:
                        self.logger.log_analysis(
                            f"Market regime analyzed for {underlying} {timeframe}",
                            analysis_type="regime_analysis",
                            extra_data=regime_state.to_dict()
                        )
                        
                        # Store regime state for alerts
                        if timeframe not in self.market_data_cache:
                            self.market_data_cache[timeframe] = {}
                        self.market_data_cache[timeframe][f"{underlying}_regime"] = regime_state
                
        except Exception as e:
            self.error_handler.record_error(e, {
                'timeframe': timeframe,
                'operation': 'data_analysis'
            })
            self.logger.error(f"Enhanced analysis failed for {timeframe}", exception=e)
    
    async def _detect_timeframe_regime_enhanced(self, timeframe: str):
        """Enhanced regime detection with circuit breaker protection"""
        circuit_breaker_name = f"regime_detection_{timeframe}"
        
        while self.is_running:
            try:
                interval = self.config_manager.get_monitoring_interval(timeframe)
                
                await self.error_handler.execute_with_retry(
                    self._analyze_market_regime_enhanced,
                    timeframe,
                    circuit_breaker_name=circuit_breaker_name
                )
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                self.error_count += 1
                self.error_handler.record_error(e, {
                    'timeframe': timeframe,
                    'operation': 'regime_detection'
                })
                await asyncio.sleep(60)
    
    async def _analyze_market_regime_enhanced(self, timeframe: str):
        """Enhanced market regime analysis"""
        # This method is now handled in _analyze_timeframe_data_enhanced
        # to avoid duplication and improve efficiency
        pass
    
    async def _generate_enhanced_alerts(self):
        """Generate enhanced alerts with better context and filtering"""
        while self.is_running:
            try:
                with self.logger.context(operation="alert_generation"):
                    # Process anomalies for alerts
                    recent_anomalies = [
                        a for a in self.anomaly_logs
                        if (datetime.now() - a.timestamp).seconds < 300  # Last 5 minutes
                    ]
                    
                    for anomaly in recent_anomalies:
                        if anomaly.severity.value >= 3:  # High or Critical
                            alert = {
                                "type": "anomaly_alert",
                                "severity": anomaly.severity.value,
                                "message": anomaly.description,
                                "underlying": anomaly.underlying,
                                "timeframe": anomaly.timeframe,
                                "timestamp": anomaly.timestamp.isoformat(),
                                "metadata": anomaly.metadata
                            }
                            self.alerts_cache.append(alert)
                    
                    # Clear old anomalies
                    cutoff_time = datetime.now() - timedelta(hours=1)
                    self.anomaly_logs = [
                        a for a in self.anomaly_logs
                        if a.timestamp > cutoff_time
                    ]
                    
                    await asyncio.sleep(60)
                    
            except Exception as e:
                self.error_handler.record_error(e, {'operation': 'alert_generation'})
                await asyncio.sleep(60)

    async def _detect_enhanced_anomalies(self):
        """Enhanced anomaly detection coordination"""
        while self.is_running:
            try:
                with self.logger.context(operation="anomaly_detection"):
                    # Get anomaly summary
                    anomaly_summary = self.anomaly_detector.get_anomaly_summary(hours=1)

                    if anomaly_summary['total_anomalies'] > 0:
                        self.logger.info(
                            "Anomaly detection summary",
                            extra_data=anomaly_summary
                        )

                    await asyncio.sleep(300)  # Every 5 minutes

            except Exception as e:
                self.error_handler.record_error(e, {'operation': 'anomaly_detection'})
                await asyncio.sleep(300)

    async def _generate_enhanced_summary(self):
        """Generate enhanced market summary with comprehensive metrics"""
        while self.is_running:
            try:
                with self.logger.performance_tracking("summary_generation"):
                    config = self.config_manager.get_config()

                    summary = {
                        "timestamp": datetime.now().isoformat(),
                        "agent_version": "enhanced_v1.0",
                        "market_regime": {},
                        "anomaly_summary": {},
                        "data_quality": {},
                        "system_health": {},
                        "alerts": []
                    }

                    # Collect market regime information
                    for timeframe in self.timeframe_manager.get_active_timeframes():
                        if timeframe in self.market_data_cache:
                            for underlying in config.underlying_symbols:
                                regime_key = f"{underlying}_regime"
                                if regime_key in self.market_data_cache[timeframe]:
                                    regime_state = self.market_data_cache[timeframe][regime_key]
                                    summary["market_regime"][f"{underlying}_{timeframe}"] = regime_state.to_dict()

                    # Add anomaly summary
                    summary["anomaly_summary"] = self.anomaly_detector.get_anomaly_summary(hours=24)

                    # Add data quality metrics
                    validation_summary = self.data_validator.get_validation_summary(hours=24)
                    summary["data_quality"] = validation_summary

                    # Add system health metrics
                    error_summary = self.error_handler.get_error_summary(hours=24)
                    file_metrics = self.file_manager.get_metrics()
                    timeframe_status = await self.timeframe_manager.get_status()

                    summary["system_health"] = {
                        "error_summary": error_summary,
                        "file_operations": file_metrics,
                        "timeframe_manager": timeframe_status,
                        "cycle_count": self.cycle_count,
                        "uptime_hours": (datetime.now() - self.last_heartbeat).total_seconds() / 3600
                    }

                    # Add recent alerts
                    recent_alerts = [
                        alert for alert in self.alerts_cache
                        if isinstance(alert.get('timestamp'), str)
                    ]
                    summary["alerts"] = recent_alerts[-10:]  # Last 10 alerts

                    # Save enhanced summary
                    output_path = Path(config.output_settings.summary_output_path)
                    success = await self.file_manager.write_json_async(summary, output_path)

                    if success:
                        self.logger.info(
                            "Enhanced market summary generated",
                            extra_data={
                                'output_path': str(output_path),
                                'regime_count': len(summary["market_regime"]),
                                'anomaly_count': summary["anomaly_summary"].get('total_anomalies', 0),
                                'alert_count': len(summary["alerts"])
                            }
                        )

                    # Clear old alerts
                    self.alerts_cache = self.alerts_cache[-50:]  # Keep last 50 alerts

                    await asyncio.sleep(60)

            except Exception as e:
                self.error_handler.record_error(e, {'operation': 'summary_generation'})
                await asyncio.sleep(60)

    async def _send_enhanced_heartbeat(self):
        """Enhanced heartbeat with comprehensive status"""
        while self.is_running:
            try:
                self.last_heartbeat = datetime.now()
                self.cycle_count += 1

                # Enhanced status data
                status_data = {
                    'agent_name': 'enhanced_market_monitoring',
                    'agent_version': 'enhanced_v1.0',
                    'status': 'running',
                    'last_activity': self.last_heartbeat.isoformat(),
                    'cycles_completed': self.cycle_count,
                    'errors_count': self.error_count,
                    'performance_score': min(1.0, max(0.0, 1.0 - (self.error_count / max(1, self.cycle_count)))),
                    'active_timeframes': self.timeframe_manager.get_active_timeframes(),
                    'circuit_breaker_status': {
                        name: cb.state.value
                        for name, cb in self.error_handler.circuit_breakers.items()
                    },
                    'file_cache_size': len(self.file_manager.file_cache),
                    'anomaly_count_24h': len([
                        a for a in self.anomaly_logs
                        if (datetime.now() - a.timestamp).total_seconds() < 86400
                    ])
                }

                # Save status to file
                status_dir = Path("data/status")
                status_dir.mkdir(parents=True, exist_ok=True)
                status_file = status_dir / "enhanced_market_monitoring_status.json"

                await self.file_manager.write_json_async(status_data, status_file)

                self.logger.debug(
                    "Enhanced heartbeat sent",
                    extra_data={'cycle': self.cycle_count, 'errors': self.error_count}
                )

                await asyncio.sleep(30)

            except Exception as e:
                self.error_count += 1
                self.error_handler.record_error(e, {'operation': 'heartbeat'})
                await asyncio.sleep(30)

    async def _monitor_system_health(self):
        """Monitor overall system health and performance"""
        while self.is_running:
            try:
                with self.logger.context(operation="health_monitoring"):
                    # Check circuit breaker states
                    for name, cb in self.error_handler.circuit_breakers.items():
                        if cb.state.value == "open":
                            self.logger.warning(
                                f"Circuit breaker {name} is open",
                                extra_data={'failure_count': cb.failure_count}
                            )

                    # Check file operation performance
                    file_metrics = self.file_manager.get_metrics()
                    if file_metrics['error_count'] > 0:
                        error_rate = file_metrics['error_count'] / max(file_metrics['operation_count'], 1)
                        if error_rate > 0.1:  # More than 10% error rate
                            self.logger.warning(
                                "High file operation error rate",
                                extra_data=file_metrics
                            )

                    # Check data validation quality
                    validation_summary = self.data_validator.get_validation_summary(hours=1)
                    if 'success_rate' in validation_summary and validation_summary['success_rate'] < 0.8:
                        self.logger.warning(
                            "Low data validation success rate",
                            extra_data=validation_summary
                        )

                    await asyncio.sleep(300)  # Every 5 minutes

            except Exception as e:
                self.error_handler.record_error(e, {'operation': 'health_monitoring'})
                await asyncio.sleep(300)

    async def _cleanup_old_data(self):
        """Enhanced cleanup with better error handling"""
        try:
            with self.logger.context(operation="cleanup"):
                config = self.config_manager.get_config()
                cutoff_time = datetime.now() - timedelta(hours=1)
                cleaned_count = 0

                if self.live_path.exists():
                    for timeframe in config.timeframes:
                        timeframe_path = self.live_path / timeframe
                        if timeframe_path.exists():
                            files = await self.file_manager.find_latest_files_async(
                                timeframe_path, "*.parquet", max_files=1000
                            )

                            for file_path in files:
                                try:
                                    file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                                    if file_mtime < cutoff_time:
                                        file_path.unlink()
                                        cleaned_count += 1
                                except Exception as e:
                                    self.logger.warning(f"Failed to remove {file_path}", exception=e)

                if cleaned_count > 0:
                    self.logger.info(f"Cleaned up {cleaned_count} old data files")

        except Exception as e:
            self.error_handler.record_error(e, {'operation': 'cleanup'})

    async def _initialize_technical_indicators(self):
        """Initialize technical indicators with enhanced error handling"""
        try:
            with self.logger.context(operation="indicator_initialization"):
                config = self.config_manager.get_config()

                for underlying in config.underlying_symbols:
                    historical_data = await self._load_historical_data_enhanced(underlying)

                    if historical_data is not None and historical_data.height >= 50:
                        # Validate historical data
                        validation_result = await self.data_validator.validate_dataframe(
                            historical_data, 'ohlc'
                        )

                        if validation_result.is_valid:
                            for row in historical_data.iter_rows(named=True):
                                ohlc_data = {
                                    'timestamp': row['timestamp'],
                                    'open': row['open'],
                                    'high': row['high'],
                                    'low': row['low'],
                                    'close': row['close'],
                                    'volume': row.get('volume', 0)
                                }
                                await self.indicators_manager.update_indicators(underlying, ohlc_data)

                            self.logger.info(
                                f"Initialized indicators for {underlying}",
                                extra_data={
                                    'rows': historical_data.height,
                                    'quality_score': validation_result.metrics.overall_score
                                }
                            )
                        else:
                            self.logger.warning(
                                f"Historical data validation failed for {underlying}",
                                extra_data={'issues': len(validation_result.issues)}
                            )
                    else:
                        self.logger.warning(f"Insufficient historical data for {underlying}")

        except Exception as e:
            self.error_handler.record_error(e, {'operation': 'indicator_initialization'})

    async def _load_historical_data_enhanced(self, underlying: str):
        """Load historical data with enhanced file operations"""
        try:
            historical_dir = Path("data/historical")
            timeframes = ["5min", "1min"]

            for timeframe in timeframes:
                data_dir = historical_dir / timeframe
                if not data_dir.exists():
                    continue

                pattern = f"{underlying}_{timeframe}_*.parquet"
                files = await self.file_manager.find_latest_files_async(
                    data_dir, pattern, max_files=1
                )

                if files:
                    latest_file = files[0]
                    data = await self.file_manager.read_parquet_async(latest_file)

                    if data and data.height >= 50:
                        return data.tail(200)

            return None

        except Exception as e:
            self.error_handler.record_error(e, {
                'underlying': underlying,
                'operation': 'load_historical_data'
            })
            return None

    def _initialize_data_structures(self):
        """Initialize data structures for active timeframes"""
        active_timeframes = self.timeframe_manager.get_active_timeframes()
        self.market_data_cache = {tf: {} for tf in active_timeframes}
        self.last_update = {tf: None for tf in active_timeframes}

    async def _on_timeframe_change(self, action: str, name: str, timeframe_info):
        """Handle timeframe changes"""
        self.logger.info(
            f"Timeframe {action}: {name}",
            extra_data={
                'action': action,
                'timeframe': name,
                'interval_seconds': timeframe_info.interval_seconds
            }
        )

        if action == "activated":
            self.market_data_cache[name] = {}
            self.last_update[name] = None
        elif action == "deactivated":
            self.market_data_cache.pop(name, None)
            self.last_update.pop(name, None)

    async def cleanup(self):
        """Enhanced cleanup with all components"""
        try:
            self.logger.info("Cleaning up Enhanced Market Monitoring Agent...")
            self.is_running = False

            # Cleanup all components
            await self.file_manager.cleanup()
            await self.timeframe_manager.cleanup_unused_timeframes()

            # Reset circuit breakers
            for name in list(self.error_handler.circuit_breakers.keys()):
                self.error_handler.reset_circuit_breaker(name)

            self.logger.info("Enhanced Market Monitoring Agent cleaned up successfully")

        except Exception as e:
            self.logger.error("Cleanup failed", exception=e)


# Example usage
async def main():
    """Main function to run the enhanced agent"""
    agent = EnhancedOptionsMarketMonitoringAgent()
    try:
        success = await agent.initialize()
        if success:
            await agent.start()
    except KeyboardInterrupt:
        print("Agent interrupted by user")
    except Exception as e:
        print(f"Error in main: {e}")
    finally:
        await agent.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
