"""
Dynamic Timeframe Manager

Manages timeframes dynamically, allowing runtime addition/removal and ensuring
consistency between hardcoded and configuration-based timeframes.
"""

import asyncio
from typing import Dict, List, Set, Optional, Callable, Any
from datetime import datetime, timedelta
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class TimeframeType(Enum):
    """Timeframe types for validation"""
    MINUTE = "min"
    HOUR = "h"
    DAY = "d"
    WEEK = "w"
    MONTH = "M"


class TimeframeInfo:
    """Information about a timeframe"""
    
    def __init__(
        self, 
        name: str, 
        interval_seconds: int, 
        display_name: Optional[str] = None,
        is_active: bool = True,
        priority: int = 1
    ):
        self.name = name
        self.interval_seconds = interval_seconds
        self.display_name = display_name or name
        self.is_active = is_active
        self.priority = priority
        self.created_at = datetime.now()
        self.last_used = datetime.now()
        
    def __str__(self):
        return f"TimeframeInfo({self.name}, {self.interval_seconds}s, active={self.is_active})"
    
    def __repr__(self):
        return self.__str__()


class TimeframeManager:
    """
    Dynamic timeframe management system.
    
    Features:
    - Runtime addition/removal of timeframes
    - Consistency validation between config and hardcoded timeframes
    - Timeframe priority management
    - Active/inactive timeframe states
    - Event-driven notifications for timeframe changes
    - Automatic cleanup of unused timeframes
    """
    
    # Standard timeframe definitions
    STANDARD_TIMEFRAMES = {
        "1min": 60,
        "3min": 180,
        "5min": 300,
        "15min": 900,
        "30min": 1800,
        "1h": 3600,
        "4h": 14400,
        "1d": 86400,
        "1w": 604800
    }
    
    def __init__(self):
        self._timeframes: Dict[str, TimeframeInfo] = {}
        self._active_timeframes: Set[str] = set()
        self._change_callbacks: List[Callable[[str, str, TimeframeInfo], None]] = []
        self._lock = asyncio.Lock()
        self._monitoring_tasks: Dict[str, asyncio.Task] = {}
        self._consistency_check_interval = 300  # 5 minutes
        
        # Initialize with standard timeframes
        self._initialize_standard_timeframes()
    
    def _initialize_standard_timeframes(self):
        """Initialize with standard timeframes"""
        for name, interval in self.STANDARD_TIMEFRAMES.items():
            self._timeframes[name] = TimeframeInfo(
                name=name,
                interval_seconds=interval,
                is_active=False,  # Will be activated when needed
                priority=self._get_default_priority(name)
            )
    
    def _get_default_priority(self, timeframe: str) -> int:
        """Get default priority for timeframe"""
        priority_map = {
            "1min": 1,
            "3min": 2,
            "5min": 3,
            "15min": 4,
            "30min": 5,
            "1h": 6,
            "4h": 7,
            "1d": 8,
            "1w": 9
        }
        return priority_map.get(timeframe, 10)
    
    async def add_timeframe(
        self, 
        name: str, 
        interval_seconds: int,
        display_name: Optional[str] = None,
        priority: int = 5,
        activate: bool = True
    ) -> bool:
        """
        Add a new timeframe.
        
        Args:
            name: Timeframe name (e.g., "2min", "45min")
            interval_seconds: Interval in seconds
            display_name: Human-readable display name
            priority: Priority for processing order (lower = higher priority)
            activate: Whether to activate immediately
            
        Returns:
            True if added successfully, False if already exists
        """
        async with self._lock:
            if name in self._timeframes:
                logger.warning(f"Timeframe {name} already exists")
                return False
            
            # Validate timeframe format
            if not self._validate_timeframe_format(name):
                logger.error(f"Invalid timeframe format: {name}")
                return False
            
            # Create timeframe info
            timeframe_info = TimeframeInfo(
                name=name,
                interval_seconds=interval_seconds,
                display_name=display_name,
                is_active=activate,
                priority=priority
            )
            
            self._timeframes[name] = timeframe_info
            
            if activate:
                self._active_timeframes.add(name)
                await self._start_monitoring_task(name)
            
            # Notify callbacks
            await self._notify_change("added", name, timeframe_info)
            
            logger.info(f"Added timeframe: {timeframe_info}")
            return True
    
    async def remove_timeframe(self, name: str) -> bool:
        """
        Remove a timeframe.
        
        Args:
            name: Timeframe name to remove
            
        Returns:
            True if removed successfully, False if not found
        """
        async with self._lock:
            if name not in self._timeframes:
                logger.warning(f"Timeframe {name} not found")
                return False
            
            timeframe_info = self._timeframes[name]
            
            # Stop monitoring task if active
            if name in self._monitoring_tasks:
                self._monitoring_tasks[name].cancel()
                del self._monitoring_tasks[name]
            
            # Remove from active set
            self._active_timeframes.discard(name)
            
            # Remove timeframe
            del self._timeframes[name]
            
            # Notify callbacks
            await self._notify_change("removed", name, timeframe_info)
            
            logger.info(f"Removed timeframe: {name}")
            return True
    
    async def activate_timeframe(self, name: str) -> bool:
        """Activate a timeframe"""
        async with self._lock:
            if name not in self._timeframes:
                logger.error(f"Timeframe {name} not found")
                return False
            
            timeframe_info = self._timeframes[name]
            if timeframe_info.is_active:
                logger.debug(f"Timeframe {name} already active")
                return True
            
            timeframe_info.is_active = True
            timeframe_info.last_used = datetime.now()
            self._active_timeframes.add(name)
            
            await self._start_monitoring_task(name)
            await self._notify_change("activated", name, timeframe_info)
            
            logger.info(f"Activated timeframe: {name}")
            return True
    
    async def deactivate_timeframe(self, name: str) -> bool:
        """Deactivate a timeframe"""
        async with self._lock:
            if name not in self._timeframes:
                logger.error(f"Timeframe {name} not found")
                return False
            
            timeframe_info = self._timeframes[name]
            if not timeframe_info.is_active:
                logger.debug(f"Timeframe {name} already inactive")
                return True
            
            timeframe_info.is_active = False
            self._active_timeframes.discard(name)
            
            # Stop monitoring task
            if name in self._monitoring_tasks:
                self._monitoring_tasks[name].cancel()
                del self._monitoring_tasks[name]
            
            await self._notify_change("deactivated", name, timeframe_info)
            
            logger.info(f"Deactivated timeframe: {name}")
            return True
    
    def get_active_timeframes(self) -> List[str]:
        """Get list of active timeframes sorted by priority"""
        active_timeframes = [
            (name, self._timeframes[name].priority)
            for name in self._active_timeframes
            if name in self._timeframes
        ]
        active_timeframes.sort(key=lambda x: x[1])  # Sort by priority
        return [name for name, _ in active_timeframes]
    
    def get_all_timeframes(self) -> Dict[str, TimeframeInfo]:
        """Get all timeframes"""
        return self._timeframes.copy()
    
    def get_timeframe_info(self, name: str) -> Optional[TimeframeInfo]:
        """Get information about a specific timeframe"""
        return self._timeframes.get(name)
    
    def get_interval_seconds(self, name: str) -> Optional[int]:
        """Get interval in seconds for a timeframe"""
        timeframe_info = self._timeframes.get(name)
        return timeframe_info.interval_seconds if timeframe_info else None
    
    async def sync_with_config(self, config_timeframes: List[str]) -> Dict[str, str]:
        """
        Synchronize with configuration timeframes.
        
        Args:
            config_timeframes: List of timeframes from configuration
            
        Returns:
            Dictionary with sync results
        """
        results = {
            "added": [],
            "activated": [],
            "deactivated": [],
            "errors": []
        }
        
        # Activate timeframes from config
        for tf in config_timeframes:
            if tf in self._timeframes:
                if not self._timeframes[tf].is_active:
                    success = await self.activate_timeframe(tf)
                    if success:
                        results["activated"].append(tf)
            else:
                # Try to add if it's a standard timeframe
                if tf in self.STANDARD_TIMEFRAMES:
                    success = await self.add_timeframe(
                        tf, 
                        self.STANDARD_TIMEFRAMES[tf],
                        activate=True
                    )
                    if success:
                        results["added"].append(tf)
                else:
                    results["errors"].append(f"Unknown timeframe: {tf}")
        
        # Deactivate timeframes not in config
        current_active = self.get_active_timeframes()
        for tf in current_active:
            if tf not in config_timeframes:
                success = await self.deactivate_timeframe(tf)
                if success:
                    results["deactivated"].append(tf)
        
        logger.info(f"Timeframe sync completed: {results}")
        return results
    
    def add_change_callback(self, callback: Callable[[str, str, TimeframeInfo], None]):
        """Add callback for timeframe changes"""
        self._change_callbacks.append(callback)
    
    def remove_change_callback(self, callback: Callable[[str, str, TimeframeInfo], None]):
        """Remove change callback"""
        if callback in self._change_callbacks:
            self._change_callbacks.remove(callback)
    
    async def _notify_change(self, action: str, name: str, timeframe_info: TimeframeInfo):
        """Notify callbacks about timeframe changes"""
        for callback in self._change_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(action, name, timeframe_info)
                else:
                    callback(action, name, timeframe_info)
            except Exception as e:
                logger.error(f"Error in timeframe change callback: {e}")
    
    async def _start_monitoring_task(self, name: str):
        """Start monitoring task for timeframe (placeholder)"""
        # This would be implemented to start actual monitoring
        # For now, just log the action
        logger.debug(f"Started monitoring task for {name}")
    
    def _validate_timeframe_format(self, name: str) -> bool:
        """Validate timeframe format"""
        import re
        pattern = r'^(\d+)(min|h|d|w|M)$'
        return bool(re.match(pattern, name))
    
    async def cleanup_unused_timeframes(self, max_age_hours: int = 24):
        """Clean up unused timeframes"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        to_remove = []
        
        for name, info in self._timeframes.items():
            if not info.is_active and info.last_used < cutoff_time:
                to_remove.append(name)
        
        for name in to_remove:
            await self.remove_timeframe(name)
        
        if to_remove:
            logger.info(f"Cleaned up unused timeframes: {to_remove}")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get timeframe manager status"""
        return {
            "total_timeframes": len(self._timeframes),
            "active_timeframes": len(self._active_timeframes),
            "active_list": self.get_active_timeframes(),
            "monitoring_tasks": len(self._monitoring_tasks),
            "callbacks_registered": len(self._change_callbacks)
        }
