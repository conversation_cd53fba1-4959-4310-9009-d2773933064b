"""
Data Validation System

Provides comprehensive data validation for parquet files with schema validation,
data type checking, null value detection, and data quality metrics.
"""

import polars as pl
import numpy as np
from typing import Dict, List, Optional, Any, Union, Tuple, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class ValidationSeverity(Enum):
    """Validation issue severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ValidationRule(Enum):
    """Types of validation rules"""
    SCHEMA_VALIDATION = "schema_validation"
    DATA_TYPE_CHECK = "data_type_check"
    NULL_VALUE_CHECK = "null_value_check"
    RANGE_CHECK = "range_check"
    UNIQUENESS_CHECK = "uniqueness_check"
    COMPLETENESS_CHECK = "completeness_check"
    CONSISTENCY_CHECK = "consistency_check"
    BUSINESS_RULE_CHECK = "business_rule_check"


@dataclass
class ValidationIssue:
    """Represents a data validation issue"""
    rule: ValidationRule
    severity: ValidationSeverity
    column: Optional[str]
    message: str
    count: int = 0
    percentage: float = 0.0
    sample_values: List[Any] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'rule': self.rule.value,
            'severity': self.severity.value,
            'column': self.column,
            'message': self.message,
            'count': self.count,
            'percentage': self.percentage,
            'sample_values': self.sample_values,
            'metadata': self.metadata
        }


@dataclass
class DataQualityMetrics:
    """Data quality metrics"""
    total_rows: int
    total_columns: int
    null_count: int
    null_percentage: float
    duplicate_rows: int
    duplicate_percentage: float
    completeness_score: float
    consistency_score: float
    validity_score: float
    overall_score: float
    column_metrics: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'total_rows': self.total_rows,
            'total_columns': self.total_columns,
            'null_count': self.null_count,
            'null_percentage': self.null_percentage,
            'duplicate_rows': self.duplicate_rows,
            'duplicate_percentage': self.duplicate_percentage,
            'completeness_score': self.completeness_score,
            'consistency_score': self.consistency_score,
            'validity_score': self.validity_score,
            'overall_score': self.overall_score,
            'column_metrics': self.column_metrics
        }


@dataclass
class ValidationResult:
    """Complete validation result"""
    is_valid: bool
    issues: List[ValidationIssue]
    metrics: DataQualityMetrics
    validation_time: datetime
    file_path: Optional[str] = None
    
    def get_issues_by_severity(self, severity: ValidationSeverity) -> List[ValidationIssue]:
        return [issue for issue in self.issues if issue.severity == severity]
    
    def has_critical_issues(self) -> bool:
        return any(issue.severity == ValidationSeverity.CRITICAL for issue in self.issues)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'is_valid': self.is_valid,
            'issues': [issue.to_dict() for issue in self.issues],
            'metrics': self.metrics.to_dict(),
            'validation_time': self.validation_time.isoformat(),
            'file_path': self.file_path,
            'summary': {
                'total_issues': len(self.issues),
                'critical_issues': len(self.get_issues_by_severity(ValidationSeverity.CRITICAL)),
                'error_issues': len(self.get_issues_by_severity(ValidationSeverity.ERROR)),
                'warning_issues': len(self.get_issues_by_severity(ValidationSeverity.WARNING))
            }
        }


class SchemaDefinition:
    """Schema definition for data validation"""
    
    def __init__(self):
        self.required_columns: Set[str] = set()
        self.column_types: Dict[str, pl.DataType] = {}
        self.nullable_columns: Set[str] = set()
        self.unique_columns: Set[str] = set()
        self.range_constraints: Dict[str, Tuple[Any, Any]] = {}
        self.business_rules: List[callable] = []
    
    def add_column(
        self, 
        name: str, 
        dtype: pl.DataType, 
        required: bool = True,
        nullable: bool = False,
        unique: bool = False,
        min_value: Any = None,
        max_value: Any = None
    ):
        """Add column definition"""
        if required:
            self.required_columns.add(name)
        
        self.column_types[name] = dtype
        
        if nullable:
            self.nullable_columns.add(name)
        
        if unique:
            self.unique_columns.add(name)
        
        if min_value is not None or max_value is not None:
            self.range_constraints[name] = (min_value, max_value)
    
    def add_business_rule(self, rule: callable):
        """Add business rule validation function"""
        self.business_rules.append(rule)


class DataValidator:
    """
    Comprehensive data validation system.
    
    Features:
    - Schema validation with flexible column definitions
    - Data type checking and conversion
    - Null value detection and handling
    - Range and constraint validation
    - Uniqueness validation
    - Data completeness assessment
    - Business rule validation
    - Data quality metrics calculation
    - Performance optimized for large datasets
    """
    
    def __init__(self):
        self.schemas: Dict[str, SchemaDefinition] = {}
        self.validation_history: List[ValidationResult] = []
        self.max_history_size = 100
        
        # Default OHLC schema
        self._setup_default_schemas()
    
    def _setup_default_schemas(self):
        """Setup default schemas for common data types"""
        # OHLC data schema
        ohlc_schema = SchemaDefinition()
        ohlc_schema.add_column('timestamp', pl.Datetime, required=True)
        ohlc_schema.add_column('open', pl.Float64, required=True, min_value=0)
        ohlc_schema.add_column('high', pl.Float64, required=True, min_value=0)
        ohlc_schema.add_column('low', pl.Float64, required=True, min_value=0)
        ohlc_schema.add_column('close', pl.Float64, required=True, min_value=0)
        ohlc_schema.add_column('volume', pl.Int64, required=False, min_value=0)
        
        # Add business rule: high >= low, high >= open, high >= close, low <= open, low <= close
        def ohlc_business_rule(df: pl.DataFrame) -> List[ValidationIssue]:
            issues = []
            
            # High should be >= all other prices
            high_violations = df.filter(
                (pl.col('high') < pl.col('low')) |
                (pl.col('high') < pl.col('open')) |
                (pl.col('high') < pl.col('close'))
            )
            
            if high_violations.height > 0:
                issues.append(ValidationIssue(
                    rule=ValidationRule.BUSINESS_RULE_CHECK,
                    severity=ValidationSeverity.ERROR,
                    column='high',
                    message='High price should be >= all other prices',
                    count=high_violations.height,
                    percentage=(high_violations.height / df.height) * 100
                ))
            
            # Low should be <= all other prices
            low_violations = df.filter(
                (pl.col('low') > pl.col('high')) |
                (pl.col('low') > pl.col('open')) |
                (pl.col('low') > pl.col('close'))
            )
            
            if low_violations.height > 0:
                issues.append(ValidationIssue(
                    rule=ValidationRule.BUSINESS_RULE_CHECK,
                    severity=ValidationSeverity.ERROR,
                    column='low',
                    message='Low price should be <= all other prices',
                    count=low_violations.height,
                    percentage=(low_violations.height / df.height) * 100
                ))
            
            return issues
        
        ohlc_schema.add_business_rule(ohlc_business_rule)
        self.schemas['ohlc'] = ohlc_schema
    
    def register_schema(self, name: str, schema: SchemaDefinition):
        """Register a custom schema"""
        self.schemas[name] = schema
    
    async def validate_dataframe(
        self, 
        data: pl.DataFrame, 
        schema_name: str = 'ohlc',
        file_path: Optional[str] = None
    ) -> ValidationResult:
        """
        Validate DataFrame against schema.
        
        Args:
            data: DataFrame to validate
            schema_name: Name of schema to use
            file_path: Optional file path for context
            
        Returns:
            ValidationResult with issues and metrics
        """
        start_time = datetime.now()
        issues = []
        
        if schema_name not in self.schemas:
            issues.append(ValidationIssue(
                rule=ValidationRule.SCHEMA_VALIDATION,
                severity=ValidationSeverity.CRITICAL,
                column=None,
                message=f"Schema '{schema_name}' not found"
            ))
            
            # Return early with critical error
            return ValidationResult(
                is_valid=False,
                issues=issues,
                metrics=DataQualityMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
                validation_time=start_time,
                file_path=file_path
            )
        
        schema = self.schemas[schema_name]
        
        try:
            # Schema validation
            issues.extend(await self._validate_schema(data, schema))
            
            # Data type validation
            issues.extend(await self._validate_data_types(data, schema))
            
            # Null value validation
            issues.extend(await self._validate_null_values(data, schema))
            
            # Range validation
            issues.extend(await self._validate_ranges(data, schema))
            
            # Uniqueness validation
            issues.extend(await self._validate_uniqueness(data, schema))
            
            # Business rule validation
            issues.extend(await self._validate_business_rules(data, schema))
            
            # Calculate data quality metrics
            metrics = await self._calculate_quality_metrics(data, issues)
            
            # Determine overall validity
            is_valid = not any(issue.severity in [ValidationSeverity.CRITICAL, ValidationSeverity.ERROR] 
                             for issue in issues)
            
            result = ValidationResult(
                is_valid=is_valid,
                issues=issues,
                metrics=metrics,
                validation_time=start_time,
                file_path=file_path
            )
            
            # Store in history
            self.validation_history.append(result)
            if len(self.validation_history) > self.max_history_size:
                self.validation_history = self.validation_history[-self.max_history_size:]
            
            return result
            
        except Exception as e:
            logger.error(f"Error during validation: {e}")
            issues.append(ValidationIssue(
                rule=ValidationRule.SCHEMA_VALIDATION,
                severity=ValidationSeverity.CRITICAL,
                column=None,
                message=f"Validation failed: {str(e)}"
            ))
            
            return ValidationResult(
                is_valid=False,
                issues=issues,
                metrics=DataQualityMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
                validation_time=start_time,
                file_path=file_path
            )
    
    async def validate_file(self, file_path: Union[str, Path], schema_name: str = 'ohlc') -> ValidationResult:
        """Validate parquet file"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                return ValidationResult(
                    is_valid=False,
                    issues=[ValidationIssue(
                        rule=ValidationRule.SCHEMA_VALIDATION,
                        severity=ValidationSeverity.CRITICAL,
                        column=None,
                        message=f"File not found: {file_path}"
                    )],
                    metrics=DataQualityMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
                    validation_time=datetime.now(),
                    file_path=str(file_path)
                )
            
            data = pl.read_parquet(file_path)
            return await self.validate_dataframe(data, schema_name, str(file_path))
            
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            return ValidationResult(
                is_valid=False,
                issues=[ValidationIssue(
                    rule=ValidationRule.SCHEMA_VALIDATION,
                    severity=ValidationSeverity.CRITICAL,
                    column=None,
                    message=f"Failed to read file: {str(e)}"
                )],
                metrics=DataQualityMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
                validation_time=datetime.now(),
                file_path=str(file_path)
            )

    async def _validate_schema(self, data: pl.DataFrame, schema: SchemaDefinition) -> List[ValidationIssue]:
        """Validate DataFrame schema"""
        issues = []

        # Check required columns
        missing_columns = schema.required_columns - set(data.columns)
        if missing_columns:
            issues.append(ValidationIssue(
                rule=ValidationRule.SCHEMA_VALIDATION,
                severity=ValidationSeverity.CRITICAL,
                column=None,
                message=f"Missing required columns: {list(missing_columns)}",
                count=len(missing_columns),
                metadata={'missing_columns': list(missing_columns)}
            ))

        # Check for unexpected columns (warning only)
        expected_columns = set(schema.column_types.keys())
        unexpected_columns = set(data.columns) - expected_columns
        if unexpected_columns:
            issues.append(ValidationIssue(
                rule=ValidationRule.SCHEMA_VALIDATION,
                severity=ValidationSeverity.WARNING,
                column=None,
                message=f"Unexpected columns found: {list(unexpected_columns)}",
                count=len(unexpected_columns),
                metadata={'unexpected_columns': list(unexpected_columns)}
            ))

        return issues

    async def _validate_data_types(self, data: pl.DataFrame, schema: SchemaDefinition) -> List[ValidationIssue]:
        """Validate data types"""
        issues = []

        for column, expected_type in schema.column_types.items():
            if column not in data.columns:
                continue

            actual_type = data[column].dtype

            # Check if types are compatible
            if not self._are_types_compatible(actual_type, expected_type):
                issues.append(ValidationIssue(
                    rule=ValidationRule.DATA_TYPE_CHECK,
                    severity=ValidationSeverity.ERROR,
                    column=column,
                    message=f"Column '{column}' has type {actual_type}, expected {expected_type}",
                    metadata={
                        'actual_type': str(actual_type),
                        'expected_type': str(expected_type)
                    }
                ))

        return issues

    async def _validate_null_values(self, data: pl.DataFrame, schema: SchemaDefinition) -> List[ValidationIssue]:
        """Validate null values"""
        issues = []

        for column in data.columns:
            if column not in schema.column_types:
                continue

            null_count = data[column].null_count()
            if null_count > 0:
                null_percentage = (null_count / data.height) * 100

                # Check if nulls are allowed
                if column not in schema.nullable_columns:
                    severity = ValidationSeverity.ERROR if null_percentage > 10 else ValidationSeverity.WARNING
                    issues.append(ValidationIssue(
                        rule=ValidationRule.NULL_VALUE_CHECK,
                        severity=severity,
                        column=column,
                        message=f"Column '{column}' contains {null_count} null values ({null_percentage:.2f}%)",
                        count=null_count,
                        percentage=null_percentage
                    ))
                elif null_percentage > 50:  # Too many nulls even if allowed
                    issues.append(ValidationIssue(
                        rule=ValidationRule.NULL_VALUE_CHECK,
                        severity=ValidationSeverity.WARNING,
                        column=column,
                        message=f"Column '{column}' has high null percentage: {null_percentage:.2f}%",
                        count=null_count,
                        percentage=null_percentage
                    ))

        return issues

    async def _validate_ranges(self, data: pl.DataFrame, schema: SchemaDefinition) -> List[ValidationIssue]:
        """Validate value ranges"""
        issues = []

        for column, (min_val, max_val) in schema.range_constraints.items():
            if column not in data.columns:
                continue

            col_data = data[column].drop_nulls()
            if col_data.len() == 0:
                continue

            violations = []

            # Check minimum value
            if min_val is not None:
                below_min = col_data.filter(pl.col(column) < min_val)
                if below_min.len() > 0:
                    violations.extend(below_min.to_list())

            # Check maximum value
            if max_val is not None:
                above_max = col_data.filter(pl.col(column) > max_val)
                if above_max.len() > 0:
                    violations.extend(above_max.to_list())

            if violations:
                violation_count = len(violations)
                violation_percentage = (violation_count / col_data.len()) * 100

                issues.append(ValidationIssue(
                    rule=ValidationRule.RANGE_CHECK,
                    severity=ValidationSeverity.ERROR if violation_percentage > 5 else ValidationSeverity.WARNING,
                    column=column,
                    message=f"Column '{column}' has {violation_count} values outside range [{min_val}, {max_val}]",
                    count=violation_count,
                    percentage=violation_percentage,
                    sample_values=violations[:5],  # Sample of violations
                    metadata={'min_value': min_val, 'max_value': max_val}
                ))

        return issues

    async def _validate_uniqueness(self, data: pl.DataFrame, schema: SchemaDefinition) -> List[ValidationIssue]:
        """Validate uniqueness constraints"""
        issues = []

        for column in schema.unique_columns:
            if column not in data.columns:
                continue

            total_count = data[column].drop_nulls().len()
            unique_count = data[column].drop_nulls().n_unique()

            if unique_count < total_count:
                duplicate_count = total_count - unique_count
                duplicate_percentage = (duplicate_count / total_count) * 100

                issues.append(ValidationIssue(
                    rule=ValidationRule.UNIQUENESS_CHECK,
                    severity=ValidationSeverity.ERROR,
                    column=column,
                    message=f"Column '{column}' should be unique but has {duplicate_count} duplicates",
                    count=duplicate_count,
                    percentage=duplicate_percentage
                ))

        return issues

    async def _validate_business_rules(self, data: pl.DataFrame, schema: SchemaDefinition) -> List[ValidationIssue]:
        """Validate business rules"""
        issues = []

        for rule_func in schema.business_rules:
            try:
                rule_issues = rule_func(data)
                issues.extend(rule_issues)
            except Exception as e:
                logger.error(f"Error in business rule validation: {e}")
                issues.append(ValidationIssue(
                    rule=ValidationRule.BUSINESS_RULE_CHECK,
                    severity=ValidationSeverity.ERROR,
                    column=None,
                    message=f"Business rule validation failed: {str(e)}"
                ))

        return issues

    async def _calculate_quality_metrics(self, data: pl.DataFrame, issues: List[ValidationIssue]) -> DataQualityMetrics:
        """Calculate data quality metrics"""
        total_rows = data.height
        total_columns = len(data.columns)

        # Calculate null statistics
        total_null_count = sum(data[col].null_count() for col in data.columns)
        null_percentage = (total_null_count / (total_rows * total_columns)) * 100 if total_rows > 0 else 0

        # Calculate duplicate rows
        duplicate_rows = total_rows - data.n_unique() if total_rows > 0 else 0
        duplicate_percentage = (duplicate_rows / total_rows) * 100 if total_rows > 0 else 0

        # Calculate quality scores
        completeness_score = max(0, 100 - null_percentage) / 100

        # Consistency score based on business rule violations
        consistency_issues = [i for i in issues if i.rule == ValidationRule.BUSINESS_RULE_CHECK]
        consistency_violations = sum(issue.count for issue in consistency_issues)
        consistency_score = max(0, 100 - (consistency_violations / max(total_rows, 1)) * 100) / 100

        # Validity score based on data type and range violations
        validity_issues = [i for i in issues if i.rule in [ValidationRule.DATA_TYPE_CHECK, ValidationRule.RANGE_CHECK]]
        validity_violations = sum(issue.count for issue in validity_issues)
        validity_score = max(0, 100 - (validity_violations / max(total_rows, 1)) * 100) / 100

        # Overall score (weighted average)
        overall_score = (completeness_score * 0.3 + consistency_score * 0.4 + validity_score * 0.3)

        # Column-level metrics
        column_metrics = {}
        for col in data.columns:
            col_null_count = data[col].null_count()
            col_null_percentage = (col_null_count / total_rows) * 100 if total_rows > 0 else 0

            column_metrics[col] = {
                'null_count': col_null_count,
                'null_percentage': col_null_percentage,
                'unique_count': data[col].n_unique(),
                'data_type': str(data[col].dtype)
            }

        return DataQualityMetrics(
            total_rows=total_rows,
            total_columns=total_columns,
            null_count=total_null_count,
            null_percentage=null_percentage,
            duplicate_rows=duplicate_rows,
            duplicate_percentage=duplicate_percentage,
            completeness_score=completeness_score,
            consistency_score=consistency_score,
            validity_score=validity_score,
            overall_score=overall_score,
            column_metrics=column_metrics
        )

    def _are_types_compatible(self, actual: pl.DataType, expected: pl.DataType) -> bool:
        """Check if data types are compatible"""
        # Exact match
        if actual == expected:
            return True

        # Numeric type compatibility
        numeric_types = {pl.Int8, pl.Int16, pl.Int32, pl.Int64, pl.UInt8, pl.UInt16, pl.UInt32, pl.UInt64, pl.Float32, pl.Float64}
        if actual in numeric_types and expected in numeric_types:
            return True

        # String type compatibility
        string_types = {pl.Utf8, pl.Categorical}
        if actual in string_types and expected in string_types:
            return True

        return False

    def get_validation_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get validation summary for recent validations"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_validations = [
            v for v in self.validation_history
            if v.validation_time > cutoff_time
        ]

        if not recent_validations:
            return {'message': 'No recent validations'}

        total_validations = len(recent_validations)
        successful_validations = sum(1 for v in recent_validations if v.is_valid)

        # Aggregate issues by type
        issue_counts = {}
        for validation in recent_validations:
            for issue in validation.issues:
                rule_name = issue.rule.value
                issue_counts[rule_name] = issue_counts.get(rule_name, 0) + 1

        # Average quality scores
        avg_completeness = np.mean([v.metrics.completeness_score for v in recent_validations])
        avg_consistency = np.mean([v.metrics.consistency_score for v in recent_validations])
        avg_validity = np.mean([v.metrics.validity_score for v in recent_validations])
        avg_overall = np.mean([v.metrics.overall_score for v in recent_validations])

        return {
            'total_validations': total_validations,
            'successful_validations': successful_validations,
            'success_rate': successful_validations / total_validations,
            'issue_counts': issue_counts,
            'average_quality_scores': {
                'completeness': avg_completeness,
                'consistency': avg_consistency,
                'validity': avg_validity,
                'overall': avg_overall
            }
        }
