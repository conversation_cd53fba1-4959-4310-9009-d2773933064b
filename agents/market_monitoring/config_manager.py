"""
Centralized Configuration Manager

Provides singleton pattern configuration management with schema validation
using Pydantic and dependency injection support for better testability.
"""

import asyncio
import yaml
import aiofiles
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class AlertThresholds(BaseModel):
    """Alert thresholds configuration schema"""
    volatility_spike: float = Field(gt=0, le=1.0, description="Volatility spike threshold")
    volume_spike: float = Field(gt=0, description="Volume spike multiplier threshold")
    price_change: float = Field(gt=0, le=1.0, description="Price change threshold")
    greeks_threshold: Dict[str, float] = Field(description="Greeks thresholds")
    oi_spike_threshold: float = Field(gt=0, le=1.0, description="Open interest spike threshold")
    iv_explosion_threshold: float = Field(gt=0, le=1.0, description="IV explosion threshold")

    @validator('greeks_threshold')
    def validate_greeks(cls, v):
        required_keys = {'delta', 'gamma', 'theta', 'vega'}
        if not all(key in v for key in required_keys):
            raise ValueError(f"Greeks threshold must contain keys: {required_keys}")
        return v


class MarketRegimeParams(BaseModel):
    """Market regime detection parameters schema"""
    trend_lookback: int = Field(gt=0, description="Periods for trend detection")
    volatility_lookback: int = Field(gt=0, description="Periods for volatility analysis")
    liquidity_volume_threshold: float = Field(gt=0, le=1.0, description="Low liquidity threshold")
    momentum_rsi_threshold: float = Field(ge=0, le=100, description="RSI overbought threshold")
    momentum_macd_signal_cross_period: int = Field(gt=0, description="MACD signal cross period")


class TimeAwareParams(BaseModel):
    """Time-aware monitoring parameters schema"""
    opening_range_minutes: int = Field(gt=0, description="Opening range duration in minutes")
    power_hour_start_minutes_before_close: int = Field(gt=0, description="Power hour start time")
    expiry_warning_days: int = Field(gt=0, description="Days before expiry to warn")


class AnomalyDetectionParams(BaseModel):
    """Anomaly detection parameters schema"""
    spread_deviation_multiplier: float = Field(gt=0, description="Spread deviation multiplier")
    oi_price_divergence_threshold: float = Field(gt=0, description="OI-price divergence threshold")
    iv_price_divergence_threshold: float = Field(gt=0, description="IV-price divergence threshold")
    algo_spike_depth_change_threshold: float = Field(gt=0, description="Algo spike depth change threshold")


class OutputSettings(BaseModel):
    """Output and logging settings schema"""
    summary_output_path: str = Field(description="Path for summary output")
    log_retraining_data: bool = Field(description="Whether to log retraining data")
    retraining_data_path: str = Field(description="Path for retraining data")


class MarketMonitoringConfig(BaseModel):
    """Complete market monitoring configuration schema"""
    underlying_symbols: List[str] = Field(min_items=1, description="List of underlying symbols to monitor")
    monitoring_intervals: Dict[str, int] = Field(description="Monitoring intervals for timeframes")
    timeframes: List[str] = Field(min_items=1, description="List of timeframes to monitor")
    alert_thresholds: AlertThresholds
    market_regime_params: MarketRegimeParams
    time_aware_params: TimeAwareParams
    anomaly_detection_params: AnomalyDetectionParams
    output_settings: OutputSettings

    @validator('monitoring_intervals')
    def validate_monitoring_intervals(cls, v):
        if not all(isinstance(interval, int) and interval > 0 for interval in v.values()):
            raise ValueError("All monitoring intervals must be positive integers")
        return v

    @validator('timeframes')
    def validate_timeframes(cls, v):
        valid_timeframes = {'1min', '3min', '5min', '15min', '30min', '1h', '4h', '1d'}
        if not all(tf in valid_timeframes for tf in v):
            raise ValueError(f"Invalid timeframes. Valid options: {valid_timeframes}")
        return v


class ConfigManager:
    """
    Centralized configuration manager with singleton pattern and schema validation.
    
    Features:
    - Singleton pattern for global configuration access
    - Pydantic schema validation
    - Async configuration loading
    - Configuration hot-reloading
    - Dependency injection support
    """
    
    _instance: Optional['ConfigManager'] = None
    _config: Optional[MarketMonitoringConfig] = None
    _config_path: Optional[Path] = None
    _last_modified: Optional[datetime] = None
    _lock = asyncio.Lock()

    def __new__(cls, config_path: Optional[Union[str, Path]] = None):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        if not hasattr(self, '_initialized'):
            self._initialized = True
            if config_path:
                self._config_path = Path(config_path)

    @classmethod
    async def get_instance(cls, config_path: Optional[Union[str, Path]] = None) -> 'ConfigManager':
        """Get singleton instance with async initialization"""
        if cls._instance is None:
            cls._instance = cls(config_path)
        
        if cls._config is None and config_path:
            await cls._instance.load_config(config_path)
        
        return cls._instance

    async def load_config(self, config_path: Optional[Union[str, Path]] = None) -> MarketMonitoringConfig:
        """Load and validate configuration from YAML file"""
        async with self._lock:
            if config_path:
                self._config_path = Path(config_path)
            
            if not self._config_path:
                raise ValueError("Configuration path not provided")

            if not self._config_path.exists():
                raise FileNotFoundError(f"Configuration file not found: {self._config_path}")

            try:
                # Check if config needs reloading
                current_modified = datetime.fromtimestamp(self._config_path.stat().st_mtime)
                if self._config and self._last_modified and current_modified <= self._last_modified:
                    return self._config

                async with aiofiles.open(self._config_path, mode="r") as f:
                    content = await f.read()
                    raw_config = yaml.safe_load(content)

                # Add timeframes from monitoring_intervals if not present
                if 'timeframes' not in raw_config and 'monitoring_intervals' in raw_config:
                    raw_config['timeframes'] = list(raw_config['monitoring_intervals'].keys())

                # Validate configuration using Pydantic
                self._config = MarketMonitoringConfig(**raw_config)
                self._last_modified = current_modified
                
                logger.info(f"Configuration loaded and validated from {self._config_path}")
                return self._config

            except Exception as e:
                logger.error(f"Failed to load configuration from {self._config_path}: {e}")
                raise

    def get_config(self) -> Optional[MarketMonitoringConfig]:
        """Get current configuration (synchronous)"""
        return self._config

    async def reload_config(self) -> MarketMonitoringConfig:
        """Force reload configuration from file"""
        self._last_modified = None
        return await self.load_config()

    def get_underlying_symbols(self) -> List[str]:
        """Get list of underlying symbols"""
        if not self._config:
            raise RuntimeError("Configuration not loaded")
        return self._config.underlying_symbols

    def get_timeframes(self) -> List[str]:
        """Get list of timeframes"""
        if not self._config:
            raise RuntimeError("Configuration not loaded")
        return self._config.timeframes

    def get_monitoring_interval(self, timeframe: str) -> int:
        """Get monitoring interval for specific timeframe"""
        if not self._config:
            raise RuntimeError("Configuration not loaded")
        return self._config.monitoring_intervals.get(timeframe, 60)

    def get_alert_threshold(self, threshold_type: str) -> Any:
        """Get specific alert threshold"""
        if not self._config:
            raise RuntimeError("Configuration not loaded")
        return getattr(self._config.alert_thresholds, threshold_type, None)

    @classmethod
    def reset_instance(cls):
        """Reset singleton instance (useful for testing)"""
        cls._instance = None
        cls._config = None
        cls._config_path = None
        cls._last_modified = None
