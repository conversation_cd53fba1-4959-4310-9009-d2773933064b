"""
Enhanced Error Handling and Resilience Module

Provides comprehensive error handling with specific exception handling,
retry mechanisms for transient errors, and circuit breaker patterns.
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Callable, Any, Type, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from functools import wraps
import traceback

logger = logging.getLogger(__name__)


class ErrorType(Enum):
    """Types of errors that can occur"""
    NETWORK_ERROR = "network_error"
    FILE_IO_ERROR = "file_io_error"
    DATA_VALIDATION_ERROR = "data_validation_error"
    CALCULATION_ERROR = "calculation_error"
    CONFIGURATION_ERROR = "configuration_error"
    TIMEOUT_ERROR = "timeout_error"
    MEMORY_ERROR = "memory_error"
    UNKNOWN_ERROR = "unknown_error"


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class ErrorRecord:
    """Record of an error occurrence"""
    error_type: ErrorType
    severity: ErrorSeverity
    message: str
    exception: Optional[Exception]
    timestamp: datetime
    context: Dict[str, Any] = field(default_factory=dict)
    stack_trace: Optional[str] = None
    retry_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'error_type': self.error_type.value,
            'severity': self.severity.value,
            'message': self.message,
            'timestamp': self.timestamp.isoformat(),
            'context': self.context,
            'retry_count': self.retry_count,
            'exception_type': type(self.exception).__name__ if self.exception else None
        }


class CircuitBreakerState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, reject requests
    HALF_OPEN = "half_open"  # Testing if service recovered


@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration"""
    failure_threshold: int = 5
    recovery_timeout: int = 60  # seconds
    success_threshold: int = 3  # for half-open state
    timeout: float = 30.0  # operation timeout


class CircuitBreaker:
    """Circuit breaker implementation for resilience"""
    
    def __init__(self, name: str, config: CircuitBreakerConfig):
        self.name = name
        self.config = config
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.next_attempt_time = None
        
    def can_execute(self) -> bool:
        """Check if operation can be executed"""
        now = time.time()
        
        if self.state == CircuitBreakerState.CLOSED:
            return True
        elif self.state == CircuitBreakerState.OPEN:
            if self.next_attempt_time and now >= self.next_attempt_time:
                self.state = CircuitBreakerState.HALF_OPEN
                self.success_count = 0
                return True
            return False
        elif self.state == CircuitBreakerState.HALF_OPEN:
            return True
        
        return False
    
    def record_success(self):
        """Record successful operation"""
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.config.success_threshold:
                self.state = CircuitBreakerState.CLOSED
                self.failure_count = 0
        elif self.state == CircuitBreakerState.CLOSED:
            self.failure_count = 0
    
    def record_failure(self):
        """Record failed operation"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.state == CircuitBreakerState.CLOSED:
            if self.failure_count >= self.config.failure_threshold:
                self.state = CircuitBreakerState.OPEN
                self.next_attempt_time = time.time() + self.config.recovery_timeout
        elif self.state == CircuitBreakerState.HALF_OPEN:
            self.state = CircuitBreakerState.OPEN
            self.next_attempt_time = time.time() + self.config.recovery_timeout


class RetryConfig:
    """Retry mechanism configuration"""
    
    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True
    ):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
    
    def get_delay(self, attempt: int) -> float:
        """Calculate delay for retry attempt"""
        delay = self.base_delay * (self.exponential_base ** (attempt - 1))
        delay = min(delay, self.max_delay)
        
        if self.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)  # Add 0-50% jitter
        
        return delay


class ErrorHandler:
    """
    Enhanced error handling and resilience system.
    
    Features:
    - Specific exception handling with categorization
    - Retry mechanisms with exponential backoff
    - Circuit breaker patterns for failing services
    - Error tracking and metrics
    - Context-aware error reporting
    - Graceful degradation strategies
    """
    
    def __init__(self):
        self.error_history: List[ErrorRecord] = []
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.error_counts: Dict[ErrorType, int] = {}
        self.max_history_size = 1000
        
        # Default retry configurations for different error types
        self.retry_configs = {
            ErrorType.NETWORK_ERROR: RetryConfig(max_attempts=3, base_delay=1.0),
            ErrorType.FILE_IO_ERROR: RetryConfig(max_attempts=2, base_delay=0.5),
            ErrorType.TIMEOUT_ERROR: RetryConfig(max_attempts=2, base_delay=2.0),
            ErrorType.CALCULATION_ERROR: RetryConfig(max_attempts=1),  # Don't retry calculation errors
            ErrorType.DATA_VALIDATION_ERROR: RetryConfig(max_attempts=1),  # Don't retry validation errors
        }
        
        # Exception type mapping
        self.exception_mapping = {
            ConnectionError: ErrorType.NETWORK_ERROR,
            TimeoutError: ErrorType.TIMEOUT_ERROR,
            FileNotFoundError: ErrorType.FILE_IO_ERROR,
            PermissionError: ErrorType.FILE_IO_ERROR,
            IOError: ErrorType.FILE_IO_ERROR,
            OSError: ErrorType.FILE_IO_ERROR,
            ValueError: ErrorType.DATA_VALIDATION_ERROR,
            TypeError: ErrorType.DATA_VALIDATION_ERROR,
            MemoryError: ErrorType.MEMORY_ERROR,
            asyncio.TimeoutError: ErrorType.TIMEOUT_ERROR,
        }
    
    def classify_error(self, exception: Exception) -> ErrorType:
        """Classify exception into error type"""
        exception_type = type(exception)
        
        # Direct mapping
        if exception_type in self.exception_mapping:
            return self.exception_mapping[exception_type]
        
        # Check inheritance
        for exc_type, error_type in self.exception_mapping.items():
            if isinstance(exception, exc_type):
                return error_type
        
        return ErrorType.UNKNOWN_ERROR
    
    def determine_severity(self, error_type: ErrorType, exception: Exception) -> ErrorSeverity:
        """Determine error severity"""
        severity_mapping = {
            ErrorType.MEMORY_ERROR: ErrorSeverity.CRITICAL,
            ErrorType.CONFIGURATION_ERROR: ErrorSeverity.HIGH,
            ErrorType.DATA_VALIDATION_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.NETWORK_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.FILE_IO_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.TIMEOUT_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.CALCULATION_ERROR: ErrorSeverity.LOW,
            ErrorType.UNKNOWN_ERROR: ErrorSeverity.MEDIUM,
        }
        
        return severity_mapping.get(error_type, ErrorSeverity.MEDIUM)
    
    def record_error(
        self, 
        exception: Exception, 
        context: Optional[Dict[str, Any]] = None,
        custom_message: Optional[str] = None
    ) -> ErrorRecord:
        """Record an error occurrence"""
        error_type = self.classify_error(exception)
        severity = self.determine_severity(error_type, exception)
        
        error_record = ErrorRecord(
            error_type=error_type,
            severity=severity,
            message=custom_message or str(exception),
            exception=exception,
            timestamp=datetime.now(),
            context=context or {},
            stack_trace=traceback.format_exc()
        )
        
        # Update error counts
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        # Store in history
        self.error_history.append(error_record)
        if len(self.error_history) > self.max_history_size:
            self.error_history = self.error_history[-self.max_history_size:]
        
        # Log error
        log_level = {
            ErrorSeverity.LOW: logging.INFO,
            ErrorSeverity.MEDIUM: logging.WARNING,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }.get(severity, logging.ERROR)
        
        logger.log(log_level, f"[{error_type.value.upper()}] {error_record.message}")
        
        return error_record
    
    def get_circuit_breaker(self, name: str, config: Optional[CircuitBreakerConfig] = None) -> CircuitBreaker:
        """Get or create circuit breaker"""
        if name not in self.circuit_breakers:
            cb_config = config or CircuitBreakerConfig()
            self.circuit_breakers[name] = CircuitBreaker(name, cb_config)
        return self.circuit_breakers[name]
    
    async def execute_with_retry(
        self,
        func: Callable,
        *args,
        error_context: Optional[Dict[str, Any]] = None,
        custom_retry_config: Optional[RetryConfig] = None,
        circuit_breaker_name: Optional[str] = None,
        **kwargs
    ) -> Any:
        """Execute function with retry logic and circuit breaker"""
        
        # Check circuit breaker
        if circuit_breaker_name:
            cb = self.get_circuit_breaker(circuit_breaker_name)
            if not cb.can_execute():
                raise Exception(f"Circuit breaker {circuit_breaker_name} is open")
        
        last_exception = None
        
        for attempt in range(1, 4):  # Default max attempts
            try:
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # Record success in circuit breaker
                if circuit_breaker_name:
                    cb.record_success()
                
                return result
                
            except Exception as e:
                last_exception = e
                error_record = self.record_error(e, error_context)
                
                # Record failure in circuit breaker
                if circuit_breaker_name:
                    cb.record_failure()
                
                # Determine if we should retry
                retry_config = custom_retry_config or self.retry_configs.get(
                    error_record.error_type, 
                    RetryConfig(max_attempts=1)
                )
                
                if attempt >= retry_config.max_attempts:
                    break
                
                # Calculate delay and wait
                delay = retry_config.get_delay(attempt)
                logger.info(f"Retrying in {delay:.2f}s (attempt {attempt}/{retry_config.max_attempts})")
                await asyncio.sleep(delay)
        
        # All retries exhausted
        if last_exception:
            raise last_exception
    
    def with_error_handling(
        self,
        error_context: Optional[Dict[str, Any]] = None,
        circuit_breaker_name: Optional[str] = None,
        retry_config: Optional[RetryConfig] = None
    ):
        """Decorator for automatic error handling"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                return await self.execute_with_retry(
                    func,
                    *args,
                    error_context=error_context,
                    custom_retry_config=retry_config,
                    circuit_breaker_name=circuit_breaker_name,
                    **kwargs
                )
            return wrapper
        return decorator
    
    def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get error summary for specified time period"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_errors = [
            error for error in self.error_history
            if error.timestamp > cutoff_time
        ]
        
        summary = {
            'total_errors': len(recent_errors),
            'by_type': {},
            'by_severity': {},
            'circuit_breakers': {},
            'error_rate': len(recent_errors) / max(hours, 1)  # errors per hour
        }
        
        # Group by type and severity
        for error in recent_errors:
            type_key = error.error_type.value
            severity_key = error.severity.name
            
            summary['by_type'][type_key] = summary['by_type'].get(type_key, 0) + 1
            summary['by_severity'][severity_key] = summary['by_severity'].get(severity_key, 0) + 1
        
        # Circuit breaker status
        for name, cb in self.circuit_breakers.items():
            summary['circuit_breakers'][name] = {
                'state': cb.state.value,
                'failure_count': cb.failure_count,
                'success_count': cb.success_count
            }
        
        return summary
    
    def reset_circuit_breaker(self, name: str) -> bool:
        """Manually reset a circuit breaker"""
        if name in self.circuit_breakers:
            cb = self.circuit_breakers[name]
            cb.state = CircuitBreakerState.CLOSED
            cb.failure_count = 0
            cb.success_count = 0
            cb.next_attempt_time = None
            logger.info(f"Circuit breaker {name} manually reset")
            return True
        return False
