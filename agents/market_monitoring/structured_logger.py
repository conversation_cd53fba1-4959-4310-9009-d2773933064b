"""
Structured Logging Enhancement Module

Provides structured logging using python-json-logger with contextual information,
log levels, and integration with monitoring dashboards.
"""

import logging
import json
import sys
import os
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum
import threading
from contextlib import contextmanager
import traceback

try:
    from pythonjsonlogger import jsonlogger
    JSON_LOGGER_AVAILABLE = True
except ImportError:
    JSON_LOGGER_AVAILABLE = False
    print("Warning: python-json-logger not available. Install with: pip install python-json-logger")


class LogLevel(Enum):
    """Enhanced log levels"""
    TRACE = 5
    DEBUG = 10
    INFO = 20
    WARNING = 30
    ERROR = 40
    CRITICAL = 50


class LogCategory(Enum):
    """Log categories for better organization"""
    SYSTEM = "system"
    MARKET_DATA = "market_data"
    ANALYSIS = "analysis"
    TRADING = "trading"
    PERFORMANCE = "performance"
    SECURITY = "security"
    USER_ACTION = "user_action"
    EXTERNAL_API = "external_api"


@dataclass
class LogContext:
    """Structured log context"""
    component: str
    operation: str
    underlying: Optional[str] = None
    timeframe: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    correlation_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {k: v for k, v in asdict(self).items() if v is not None}


class PerformanceMetrics:
    """Performance metrics for logging"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.end_time = None
        self.duration_ms = None
        self.memory_usage_mb = None
        self.cpu_usage_percent = None
    
    def finish(self):
        """Mark operation as finished and calculate metrics"""
        self.end_time = datetime.now()
        self.duration_ms = (self.end_time - self.start_time).total_seconds() * 1000
        
        # Get memory usage if psutil is available
        try:
            import psutil
            process = psutil.Process()
            self.memory_usage_mb = process.memory_info().rss / 1024 / 1024
            self.cpu_usage_percent = process.cpu_percent()
        except ImportError:
            pass
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'duration_ms': self.duration_ms,
            'memory_usage_mb': self.memory_usage_mb,
            'cpu_usage_percent': self.cpu_usage_percent
        }


class CustomJsonFormatter(jsonlogger.JsonFormatter):
    """Custom JSON formatter with enhanced fields"""
    
    def add_fields(self, log_record, record, message_dict):
        super().add_fields(log_record, record, message_dict)
        
        # Add timestamp in ISO format
        log_record['timestamp'] = datetime.utcnow().isoformat() + 'Z'
        
        # Add log level name
        log_record['level'] = record.levelname
        
        # Add thread information
        log_record['thread_id'] = threading.current_thread().ident
        log_record['thread_name'] = threading.current_thread().name
        
        # Add process information
        log_record['process_id'] = os.getpid()
        
        # Add source location
        log_record['source'] = {
            'file': record.filename,
            'function': record.funcName,
            'line': record.lineno
        }


class StructuredLogger:
    """
    Enhanced structured logging system.
    
    Features:
    - JSON structured logging with python-json-logger
    - Contextual information tracking
    - Performance metrics integration
    - Multiple output destinations (file, console, external systems)
    - Log correlation and tracing
    - Automatic error context capture
    - Integration with monitoring dashboards
    """
    
    def __init__(
        self, 
        name: str = "market_monitoring",
        log_level: LogLevel = LogLevel.INFO,
        log_file: Optional[str] = None,
        enable_console: bool = True,
        enable_json: bool = True
    ):
        self.name = name
        self.logger = logging.getLogger(name)
        self.logger.setLevel(log_level.value)
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Context storage (thread-local)
        self._context = threading.local()
        
        # Setup handlers
        if enable_console:
            self._setup_console_handler(enable_json)
        
        if log_file:
            self._setup_file_handler(log_file, enable_json)
        
        # Performance tracking
        self._performance_metrics: Dict[str, PerformanceMetrics] = {}
        
        # Log statistics
        self.log_counts = {level.name: 0 for level in LogLevel}
        
    def _setup_console_handler(self, enable_json: bool):
        """Setup console logging handler"""
        console_handler = logging.StreamHandler(sys.stdout)
        
        if enable_json and JSON_LOGGER_AVAILABLE:
            formatter = CustomJsonFormatter(
                '%(timestamp)s %(level)s %(name)s %(message)s'
            )
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def _setup_file_handler(self, log_file: str, enable_json: bool):
        """Setup file logging handler"""
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file)
        
        if enable_json and JSON_LOGGER_AVAILABLE:
            formatter = CustomJsonFormatter(
                '%(timestamp)s %(level)s %(name)s %(message)s'
            )
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    @contextmanager
    def context(self, **context_data):
        """Context manager for adding structured context"""
        # Store previous context
        previous_context = getattr(self._context, 'data', {})
        
        # Merge with new context
        new_context = {**previous_context, **context_data}
        self._context.data = new_context
        
        try:
            yield
        finally:
            # Restore previous context
            self._context.data = previous_context
    
    def _get_context(self) -> Dict[str, Any]:
        """Get current context data"""
        return getattr(self._context, 'data', {})
    
    def _log_with_context(
        self, 
        level: LogLevel, 
        message: str, 
        category: Optional[LogCategory] = None,
        extra_data: Optional[Dict[str, Any]] = None,
        exception: Optional[Exception] = None
    ):
        """Log message with structured context"""
        # Prepare log data
        log_data = {
            'message': message,
            'category': category.value if category else None,
            **self._get_context()
        }
        
        # Add extra data
        if extra_data:
            log_data.update(extra_data)
        
        # Add exception information
        if exception:
            log_data['exception'] = {
                'type': type(exception).__name__,
                'message': str(exception),
                'traceback': traceback.format_exc()
            }
        
        # Update log counts
        level_name = level.name
        self.log_counts[level_name] = self.log_counts.get(level_name, 0) + 1
        
        # Log the message
        if JSON_LOGGER_AVAILABLE:
            self.logger.log(level.value, message, extra=log_data)
        else:
            # Fallback to regular logging with JSON string
            json_str = json.dumps(log_data, default=str)
            self.logger.log(level.value, f"{message} | {json_str}")
    
    def trace(self, message: str, **kwargs):
        """Log trace message"""
        self._log_with_context(LogLevel.TRACE, message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug message"""
        self._log_with_context(LogLevel.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message"""
        self._log_with_context(LogLevel.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message"""
        self._log_with_context(LogLevel.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message"""
        self._log_with_context(LogLevel.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message"""
        self._log_with_context(LogLevel.CRITICAL, message, **kwargs)
    
    def log_market_data(self, message: str, underlying: str, timeframe: str, **kwargs):
        """Log market data related message"""
        with self.context(underlying=underlying, timeframe=timeframe):
            self._log_with_context(
                LogLevel.INFO, 
                message, 
                category=LogCategory.MARKET_DATA,
                **kwargs
            )
    
    def log_analysis(self, message: str, analysis_type: str, **kwargs):
        """Log analysis related message"""
        with self.context(analysis_type=analysis_type):
            self._log_with_context(
                LogLevel.INFO,
                message,
                category=LogCategory.ANALYSIS,
                **kwargs
            )
    
    def log_performance(self, operation: str, metrics: PerformanceMetrics, **kwargs):
        """Log performance metrics"""
        self._log_with_context(
            LogLevel.INFO,
            f"Performance: {operation}",
            category=LogCategory.PERFORMANCE,
            extra_data={
                'operation': operation,
                'performance': metrics.to_dict()
            },
            **kwargs
        )
    
    def log_error_with_context(
        self, 
        message: str, 
        exception: Exception, 
        context: Optional[Dict[str, Any]] = None
    ):
        """Log error with full context"""
        error_context = {
            'error_type': type(exception).__name__,
            'error_message': str(exception),
            **(context or {})
        }
        
        self._log_with_context(
            LogLevel.ERROR,
            message,
            extra_data=error_context,
            exception=exception
        )
    
    @contextmanager
    def performance_tracking(self, operation: str):
        """Context manager for performance tracking"""
        metrics = PerformanceMetrics()
        self._performance_metrics[operation] = metrics
        
        try:
            yield metrics
        finally:
            metrics.finish()
            self.log_performance(operation, metrics)
            # Clean up old metrics
            if len(self._performance_metrics) > 100:
                oldest_key = min(self._performance_metrics.keys())
                del self._performance_metrics[oldest_key]
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """Get logging statistics"""
        return {
            'log_counts': self.log_counts.copy(),
            'total_logs': sum(self.log_counts.values()),
            'active_contexts': len(getattr(self._context, 'data', {})),
            'performance_metrics_count': len(self._performance_metrics)
        }
    
    def set_level(self, level: LogLevel):
        """Set logging level"""
        self.logger.setLevel(level.value)
    
    def add_handler(self, handler: logging.Handler):
        """Add custom logging handler"""
        self.logger.addHandler(handler)


# Global structured logger instance
structured_logger = StructuredLogger()


def get_logger(name: str = "market_monitoring") -> StructuredLogger:
    """Get structured logger instance"""
    return StructuredLogger(name)
