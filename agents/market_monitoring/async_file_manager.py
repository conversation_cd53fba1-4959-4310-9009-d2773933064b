"""
Asynchronous File I/O Manager

Provides non-blocking file operations using thread pool executor for heavy
file operations like pl.read_parquet to prevent blocking the event loop.
"""

import asyncio
import polars as pl
import aiofiles
import json
import yaml
from pathlib import Path
from typing import Optional, Dict, Any, List, Union, Callable
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from datetime import datetime, timedelta
import logging
import functools
import time

logger = logging.getLogger(__name__)


class FileOperationMetrics:
    """Track file operation performance metrics"""
    
    def __init__(self):
        self.operation_count = 0
        self.total_time = 0.0
        self.avg_time = 0.0
        self.max_time = 0.0
        self.min_time = float('inf')
        self.error_count = 0
        
    def record_operation(self, duration: float, success: bool = True):
        """Record a file operation"""
        self.operation_count += 1
        if success:
            self.total_time += duration
            self.avg_time = self.total_time / (self.operation_count - self.error_count)
            self.max_time = max(self.max_time, duration)
            self.min_time = min(self.min_time, duration)
        else:
            self.error_count += 1


class AsyncFileManager:
    """
    Asynchronous file operations manager with thread pool optimization.
    
    Features:
    - Non-blocking parquet file operations
    - Thread pool executor for CPU-intensive operations
    - File operation caching
    - Performance metrics tracking
    - Automatic retry mechanisms
    - File age validation
    """
    
    def __init__(self, max_workers: int = 4, max_cache_size: int = 100):
        self.thread_executor = ThreadPoolExecutor(max_workers=max_workers)
        self.process_executor = ProcessPoolExecutor(max_workers=max_workers // 2)
        self.file_cache: Dict[str, Dict[str, Any]] = {}
        self.max_cache_size = max_cache_size
        self.metrics = FileOperationMetrics()
        self.cache_ttl = timedelta(minutes=5)  # Cache TTL
        
    async def read_parquet_async(
        self, 
        file_path: Union[str, Path], 
        use_cache: bool = True,
        max_age_hours: Optional[float] = None
    ) -> Optional[pl.DataFrame]:
        """
        Asynchronously read parquet file using thread pool executor.
        
        Args:
            file_path: Path to parquet file
            use_cache: Whether to use file caching
            max_age_hours: Maximum file age in hours (None to skip check)
            
        Returns:
            Polars DataFrame or None if file doesn't exist/is too old
        """
        file_path = Path(file_path)
        cache_key = str(file_path.absolute())
        
        # Check cache first
        if use_cache and cache_key in self.file_cache:
            cache_entry = self.file_cache[cache_key]
            if datetime.now() - cache_entry['timestamp'] < self.cache_ttl:
                logger.debug(f"Cache hit for {file_path.name}")
                return cache_entry['data']
        
        # Check if file exists and age
        if not file_path.exists():
            logger.debug(f"File does not exist: {file_path}")
            return None
            
        if max_age_hours:
            file_age = datetime.now() - datetime.fromtimestamp(file_path.stat().st_mtime)
            if file_age > timedelta(hours=max_age_hours):
                logger.warning(f"File too old ({file_age}): {file_path}")
                return None
        
        start_time = time.time()
        try:
            # Use thread pool executor for blocking I/O
            loop = asyncio.get_event_loop()
            data = await loop.run_in_executor(
                self.thread_executor,
                self._read_parquet_sync,
                file_path
            )
            
            duration = time.time() - start_time
            self.metrics.record_operation(duration, True)
            
            # Cache the result
            if use_cache and data is not None:
                self._update_cache(cache_key, data)
            
            logger.debug(f"Read {file_path.name}: {data.height if data else 0} rows in {duration:.3f}s")
            return data
            
        except Exception as e:
            duration = time.time() - start_time
            self.metrics.record_operation(duration, False)
            logger.error(f"Failed to read {file_path}: {e}")
            return None
    
    def _read_parquet_sync(self, file_path: Path) -> Optional[pl.DataFrame]:
        """Synchronous parquet reading function for thread executor"""
        try:
            return pl.read_parquet(file_path)
        except Exception as e:
            logger.error(f"Sync read failed for {file_path}: {e}")
            return None
    
    async def read_multiple_parquet_async(
        self, 
        file_paths: List[Union[str, Path]],
        use_cache: bool = True,
        max_age_hours: Optional[float] = None,
        max_concurrent: int = 5
    ) -> Dict[str, Optional[pl.DataFrame]]:
        """
        Read multiple parquet files concurrently.
        
        Args:
            file_paths: List of file paths
            use_cache: Whether to use caching
            max_age_hours: Maximum file age in hours
            max_concurrent: Maximum concurrent operations
            
        Returns:
            Dictionary mapping file paths to DataFrames
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def read_with_semaphore(path):
            async with semaphore:
                return await self.read_parquet_async(path, use_cache, max_age_hours)
        
        tasks = [read_with_semaphore(path) for path in file_paths]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        return {
            str(path): result if not isinstance(result, Exception) else None
            for path, result in zip(file_paths, results)
        }
    
    async def write_json_async(
        self, 
        data: Dict[str, Any], 
        file_path: Union[str, Path],
        indent: int = 2
    ) -> bool:
        """
        Asynchronously write JSON data to file.
        
        Args:
            data: Data to write
            file_path: Output file path
            indent: JSON indentation
            
        Returns:
            True if successful, False otherwise
        """
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            async with aiofiles.open(file_path, mode="w") as f:
                json_str = json.dumps(data, indent=indent, default=str)
                await f.write(json_str)
            return True
        except Exception as e:
            logger.error(f"Failed to write JSON to {file_path}: {e}")
            return False
    
    async def write_parquet_async(
        self, 
        data: pl.DataFrame, 
        file_path: Union[str, Path]
    ) -> bool:
        """
        Asynchronously write DataFrame to parquet file.
        
        Args:
            data: DataFrame to write
            file_path: Output file path
            
        Returns:
            True if successful, False otherwise
        """
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                self.thread_executor,
                data.write_parquet,
                str(file_path)
            )
            return True
        except Exception as e:
            logger.error(f"Failed to write parquet to {file_path}: {e}")
            return False
    
    async def find_latest_files_async(
        self, 
        directory: Union[str, Path],
        pattern: str,
        max_files: int = 10
    ) -> List[Path]:
        """
        Asynchronously find latest files matching pattern.
        
        Args:
            directory: Directory to search
            pattern: File pattern (glob)
            max_files: Maximum number of files to return
            
        Returns:
            List of file paths sorted by modification time (newest first)
        """
        directory = Path(directory)
        if not directory.exists():
            return []
        
        try:
            loop = asyncio.get_event_loop()
            files = await loop.run_in_executor(
                self.thread_executor,
                self._find_files_sync,
                directory,
                pattern,
                max_files
            )
            return files
        except Exception as e:
            logger.error(f"Failed to find files in {directory}: {e}")
            return []
    
    def _find_files_sync(self, directory: Path, pattern: str, max_files: int) -> List[Path]:
        """Synchronous file finding for thread executor"""
        try:
            files = list(directory.glob(pattern))
            files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            return files[:max_files]
        except Exception as e:
            logger.error(f"Sync file search failed: {e}")
            return []
    
    def _update_cache(self, cache_key: str, data: pl.DataFrame):
        """Update file cache with size management"""
        if len(self.file_cache) >= self.max_cache_size:
            # Remove oldest entry
            oldest_key = min(
                self.file_cache.keys(),
                key=lambda k: self.file_cache[k]['timestamp']
            )
            del self.file_cache[oldest_key]
        
        self.file_cache[cache_key] = {
            'data': data,
            'timestamp': datetime.now()
        }
    
    def clear_cache(self):
        """Clear file cache"""
        self.file_cache.clear()
        logger.info("File cache cleared")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get file operation metrics"""
        return {
            'operation_count': self.metrics.operation_count,
            'avg_time': self.metrics.avg_time,
            'max_time': self.metrics.max_time,
            'min_time': self.metrics.min_time if self.metrics.min_time != float('inf') else 0,
            'error_count': self.metrics.error_count,
            'success_rate': (self.metrics.operation_count - self.metrics.error_count) / max(1, self.metrics.operation_count),
            'cache_size': len(self.file_cache)
        }
    
    async def cleanup(self):
        """Cleanup resources"""
        self.thread_executor.shutdown(wait=True)
        self.process_executor.shutdown(wait=True)
        self.clear_cache()
        logger.info("AsyncFileManager cleaned up")
