# Enhanced Market Monitoring System

This directory contains the enhanced market monitoring system with modular architecture and advanced features for comprehensive market surveillance.

## Overview

The enhanced market monitoring system addresses the following improvements over the original agent:

1. **Centralized Configuration Management** - Schema-validated configuration with dependency injection
2. **Asynchronous File I/O Optimization** - Non-blocking file operations with thread pool executors
3. **Dynamic Timeframe Management** - Runtime timeframe addition/removal with consistency validation
4. **Enhanced Anomaly Detection** - Statistical methods, ML models, and cross-timeframe analysis
5. **Refined Market Regime Analysis** - Multiple indicators, state-space models, and volatility detection
6. **Enhanced Error Handling** - Circuit breakers, retry mechanisms, and specific exception handling
7. **Structured Logging** - JSON logging with contextual information and monitoring integration
8. **Data Validation** - Schema validation, quality metrics, and data integrity checks

## Module Structure

```
agents/market_monitoring/
├── __init__.py                     # Module exports
├── config_manager.py               # Centralized configuration management
├── async_file_manager.py           # Asynchronous file operations
├── timeframe_manager.py            # Dynamic timeframe management
├── anomaly_detector.py             # Enhanced anomaly detection
├── market_regime_analyzer.py       # Advanced market regime analysis
├── error_handler.py                # Error handling and resilience
├── structured_logger.py            # Structured logging system
├── data_validator.py               # Data validation and quality metrics
└── README.md                       # This documentation
```

## Key Features

### 1. Centralized Configuration Management (`config_manager.py`)

- **Singleton Pattern**: Global configuration access with thread-safe initialization
- **Pydantic Validation**: Schema validation with type checking and constraints
- **Hot Reloading**: Configuration changes without restart
- **Dependency Injection**: Testable configuration management

```python
from agents.market_monitoring import ConfigManager

config_manager = await ConfigManager.get_instance("config/monitoring.yaml")
config = await config_manager.load_config()
```

### 2. Asynchronous File I/O (`async_file_manager.py`)

- **Thread Pool Execution**: Non-blocking parquet file operations
- **File Caching**: Intelligent caching with TTL and size management
- **Performance Metrics**: Operation timing and success rate tracking
- **Concurrent Operations**: Controlled concurrent file access

```python
from agents.market_monitoring import AsyncFileManager

file_manager = AsyncFileManager(max_workers=4)
data = await file_manager.read_parquet_async("data.parquet", use_cache=True)
```

### 3. Dynamic Timeframe Management (`timeframe_manager.py`)

- **Runtime Management**: Add/remove timeframes without restart
- **Consistency Validation**: Ensure config and runtime timeframes match
- **Event Callbacks**: Notifications for timeframe changes
- **Priority Management**: Timeframe processing order control

```python
from agents.market_monitoring import TimeframeManager

tf_manager = TimeframeManager()
await tf_manager.add_timeframe("2min", 120, activate=True)
active_timeframes = tf_manager.get_active_timeframes()
```

### 4. Enhanced Anomaly Detection (`anomaly_detector.py`)

- **Statistical Methods**: Z-score, IQR, rolling statistics
- **Machine Learning**: Isolation Forest for pattern detection
- **Cross-Timeframe Analysis**: Correlation across different timeframes
- **Severity Classification**: Automatic severity assessment

```python
from agents.market_monitoring import AnomalyDetector

detector = AnomalyDetector()
anomalies = await detector.detect_anomalies(data, "NIFTY", "5min")
```

### 5. Market Regime Analysis (`market_regime_analyzer.py`)

- **Multiple Indicators**: ADX, MACD, Stochastic, RSI, ATR, Bollinger Bands
- **Regime Classification**: Trend, momentum, volatility, and liquidity regimes
- **Confidence Scoring**: Reliability assessment of regime detection
- **Historical Tracking**: Regime change history and stability metrics

```python
from agents.market_monitoring import MarketRegimeAnalyzer

analyzer = MarketRegimeAnalyzer()
regime = await analyzer.analyze_market_regime(data, "NIFTY", "15min")
```

### 6. Error Handling and Resilience (`error_handler.py`)

- **Circuit Breakers**: Prevent cascade failures with automatic recovery
- **Retry Mechanisms**: Exponential backoff with jitter
- **Error Classification**: Automatic error type and severity detection
- **Context Preservation**: Rich error context for debugging

```python
from agents.market_monitoring import ErrorHandler

error_handler = ErrorHandler()

@error_handler.with_error_handling(circuit_breaker_name="data_processing")
async def process_data():
    # Your code here
    pass
```

### 7. Structured Logging (`structured_logger.py`)

- **JSON Format**: Machine-readable log output
- **Contextual Information**: Thread, process, and operation context
- **Performance Tracking**: Built-in performance measurement
- **Multiple Outputs**: Console, file, and external system integration

```python
from agents.market_monitoring import StructuredLogger

logger = StructuredLogger("market_monitoring")

with logger.context(underlying="NIFTY", timeframe="5min"):
    logger.info("Processing market data", extra_data={"rows": 100})
```

### 8. Data Validation (`data_validator.py`)

- **Schema Validation**: Flexible column and type definitions
- **Quality Metrics**: Completeness, consistency, and validity scores
- **Business Rules**: Custom validation logic
- **Performance Optimized**: Efficient validation for large datasets

```python
from agents.market_monitoring import DataValidator

validator = DataValidator()
result = await validator.validate_dataframe(data, schema_name="ohlc")
print(f"Data quality score: {result.metrics.overall_score}")
```

## Usage

### Basic Usage

```python
from agents.enhanced_options_market_monitoring_agent import EnhancedOptionsMarketMonitoringAgent

# Create and initialize agent
agent = EnhancedOptionsMarketMonitoringAgent()
success = await agent.initialize()

if success:
    # Start monitoring
    await agent.start()
```

### Advanced Configuration

```python
# Custom configuration path
agent = EnhancedOptionsMarketMonitoringAgent("custom_config.yaml")

# Access individual components
config = agent.config_manager.get_config()
file_metrics = agent.file_manager.get_metrics()
anomaly_summary = agent.anomaly_detector.get_anomaly_summary()
```

## Testing

Run the test suite to verify all components:

```bash
python test_enhanced_market_monitoring.py
```

The test suite validates:
- Individual module functionality
- Integration between components
- Configuration loading and validation
- Error handling and recovery
- Performance metrics collection

## Configuration Schema

The enhanced system uses Pydantic for configuration validation. See `config_manager.py` for the complete schema definition.

Example configuration:
```yaml
underlying_symbols:
  - NIFTY
  - BANKNIFTY

timeframes:
  - 1min
  - 5min
  - 15min

monitoring_intervals:
  1min: 60
  5min: 300
  15min: 900

alert_thresholds:
  volatility_spike: 0.20
  volume_spike: 2.0
  price_change: 0.05
```

## Performance Considerations

- **Memory Usage**: File caching and history limits prevent memory leaks
- **CPU Usage**: Thread pool executors prevent blocking operations
- **I/O Optimization**: Concurrent file operations with rate limiting
- **Error Recovery**: Circuit breakers prevent resource exhaustion

## Monitoring and Observability

The enhanced system provides comprehensive monitoring:

- **Health Checks**: System component status monitoring
- **Performance Metrics**: Operation timing and success rates
- **Error Tracking**: Detailed error classification and history
- **Quality Metrics**: Data validation and quality scores

## Migration from Original Agent

The enhanced agent maintains backward compatibility while providing new features:

1. Replace imports to use `EnhancedOptionsMarketMonitoringAgent`
2. Update configuration to use Pydantic schema validation
3. Leverage new modules for specific functionality
4. Monitor enhanced metrics and logging output

## Dependencies

Additional dependencies for enhanced features:
- `pydantic` - Configuration schema validation
- `scikit-learn` - Machine learning anomaly detection
- `python-json-logger` - Structured JSON logging (optional)
- `psutil` - System performance metrics (optional)

Install with:
```bash
pip install pydantic scikit-learn python-json-logger psutil
```
