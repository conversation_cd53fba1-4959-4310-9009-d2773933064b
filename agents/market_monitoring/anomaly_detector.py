"""
Enhanced Anomaly Detection Module

Provides advanced anomaly detection with statistical methods, machine learning models,
and cross-timeframe correlation analysis for comprehensive market surveillance.
"""

import numpy as np
import polars as pl
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import logging
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import DBSCAN
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class AnomalyType(Enum):
    """Types of anomalies that can be detected"""
    VOLUME_PRICE_DIVERGENCE = "volume_price_divergence"
    STATISTICAL_OUTLIER = "statistical_outlier"
    CROSS_TIMEFRAME_DIVERGENCE = "cross_timeframe_divergence"
    LIQUIDITY_ANOMALY = "liquidity_anomaly"
    VOLATILITY_SPIKE = "volatility_spike"
    PATTERN_BREAK = "pattern_break"
    ML_ANOMALY = "ml_anomaly"


class AnomalySeverity(Enum):
    """Severity levels for anomalies"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class AnomalyResult:
    """Result of anomaly detection"""
    type: AnomalyType
    severity: AnomalySeverity
    score: float
    description: str
    underlying: str
    timeframe: str
    timestamp: datetime
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'type': self.type.value,
            'severity': self.severity.value,
            'score': self.score,
            'description': self.description,
            'underlying': self.underlying,
            'timeframe': self.timeframe,
            'timestamp': self.timestamp.isoformat(),
            'metadata': self.metadata
        }


class StatisticalAnomalyDetector:
    """Statistical anomaly detection methods"""
    
    @staticmethod
    def z_score_anomaly(data: np.ndarray, threshold: float = 3.0) -> Tuple[np.ndarray, np.ndarray]:
        """Detect anomalies using Z-score method"""
        mean = np.mean(data)
        std = np.std(data)
        z_scores = np.abs((data - mean) / std) if std > 0 else np.zeros_like(data)
        anomalies = z_scores > threshold
        return anomalies, z_scores
    
    @staticmethod
    def iqr_anomaly(data: np.ndarray, multiplier: float = 1.5) -> Tuple[np.ndarray, np.ndarray]:
        """Detect anomalies using Interquartile Range method"""
        q1 = np.percentile(data, 25)
        q3 = np.percentile(data, 75)
        iqr = q3 - q1
        lower_bound = q1 - multiplier * iqr
        upper_bound = q3 + multiplier * iqr
        
        anomalies = (data < lower_bound) | (data > upper_bound)
        scores = np.maximum(
            (lower_bound - data) / iqr,
            (data - upper_bound) / iqr
        )
        scores = np.maximum(scores, 0)
        return anomalies, scores
    
    @staticmethod
    def rolling_anomaly(
        data: np.ndarray, 
        window: int = 20, 
        threshold: float = 2.0
    ) -> Tuple[np.ndarray, np.ndarray]:
        """Detect anomalies using rolling statistics"""
        if len(data) < window:
            return np.zeros(len(data), dtype=bool), np.zeros(len(data))
        
        anomalies = np.zeros(len(data), dtype=bool)
        scores = np.zeros(len(data))
        
        for i in range(window, len(data)):
            window_data = data[i-window:i]
            mean = np.mean(window_data)
            std = np.std(window_data)
            
            if std > 0:
                z_score = abs((data[i] - mean) / std)
                if z_score > threshold:
                    anomalies[i] = True
                    scores[i] = z_score
        
        return anomalies, scores


class MLAnomalyDetector:
    """Machine learning-based anomaly detection"""
    
    def __init__(self, contamination: float = 0.1):
        self.contamination = contamination
        self.isolation_forest = IsolationForest(
            contamination=contamination,
            random_state=42,
            n_estimators=100
        )
        self.scaler = StandardScaler()
        self.is_fitted = False
    
    def fit(self, features: np.ndarray) -> bool:
        """Fit the anomaly detection model"""
        try:
            if features.shape[0] < 10:  # Need minimum samples
                return False
            
            # Scale features
            features_scaled = self.scaler.fit_transform(features)
            
            # Fit isolation forest
            self.isolation_forest.fit(features_scaled)
            self.is_fitted = True
            return True
        except Exception as e:
            logger.error(f"Failed to fit ML anomaly detector: {e}")
            return False
    
    def predict(self, features: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Predict anomalies"""
        if not self.is_fitted:
            return np.zeros(features.shape[0], dtype=bool), np.zeros(features.shape[0])
        
        try:
            features_scaled = self.scaler.transform(features)
            predictions = self.isolation_forest.predict(features_scaled)
            scores = self.isolation_forest.decision_function(features_scaled)
            
            # Convert to boolean anomalies (Isolation Forest returns -1 for anomalies)
            anomalies = predictions == -1
            # Convert scores to positive values (higher = more anomalous)
            anomaly_scores = -scores
            
            return anomalies, anomaly_scores
        except Exception as e:
            logger.error(f"Failed to predict anomalies: {e}")
            return np.zeros(features.shape[0], dtype=bool), np.zeros(features.shape[0])


class AnomalyDetector:
    """
    Enhanced anomaly detection system.
    
    Features:
    - Statistical methods (Z-score, IQR, rolling statistics)
    - Machine learning models (Isolation Forest)
    - Cross-timeframe correlation analysis
    - Volume-price divergence detection
    - Liquidity anomaly detection
    - Pattern break detection
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or self._get_default_config()
        self.ml_detector = MLAnomalyDetector(contamination=self.config.get('ml_contamination', 0.1))
        self.historical_data: Dict[str, Dict[str, pl.DataFrame]] = {}
        self.anomaly_history: List[AnomalyResult] = []
        self.max_history_size = 1000
        
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'z_score_threshold': 3.0,
            'iqr_multiplier': 1.5,
            'rolling_window': 20,
            'rolling_threshold': 2.0,
            'volume_price_threshold': 0.01,
            'cross_timeframe_threshold': 0.05,
            'ml_contamination': 0.1,
            'min_data_points': 20
        }
    
    async def detect_anomalies(
        self, 
        data: pl.DataFrame, 
        underlying: str, 
        timeframe: str,
        cross_timeframe_data: Optional[Dict[str, pl.DataFrame]] = None
    ) -> List[AnomalyResult]:
        """
        Detect anomalies in market data.
        
        Args:
            data: Market data DataFrame
            underlying: Underlying symbol
            timeframe: Timeframe
            cross_timeframe_data: Data from other timeframes for correlation analysis
            
        Returns:
            List of detected anomalies
        """
        anomalies = []
        
        if data.height < self.config['min_data_points']:
            return anomalies
        
        try:
            # Store data for cross-timeframe analysis
            if underlying not in self.historical_data:
                self.historical_data[underlying] = {}
            self.historical_data[underlying][timeframe] = data
            
            # Statistical anomaly detection
            anomalies.extend(await self._detect_statistical_anomalies(data, underlying, timeframe))
            
            # Volume-price divergence
            anomalies.extend(await self._detect_volume_price_divergence(data, underlying, timeframe))
            
            # Cross-timeframe analysis
            if cross_timeframe_data:
                anomalies.extend(await self._detect_cross_timeframe_anomalies(
                    data, underlying, timeframe, cross_timeframe_data
                ))
            
            # ML-based detection
            anomalies.extend(await self._detect_ml_anomalies(data, underlying, timeframe))
            
            # Liquidity anomalies
            anomalies.extend(await self._detect_liquidity_anomalies(data, underlying, timeframe))
            
            # Store anomalies in history
            self.anomaly_history.extend(anomalies)
            if len(self.anomaly_history) > self.max_history_size:
                self.anomaly_history = self.anomaly_history[-self.max_history_size:]
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Error detecting anomalies for {underlying} {timeframe}: {e}")
            return []
    
    async def _detect_statistical_anomalies(
        self, 
        data: pl.DataFrame, 
        underlying: str, 
        timeframe: str
    ) -> List[AnomalyResult]:
        """Detect statistical anomalies"""
        anomalies = []
        
        try:
            # Price anomalies
            prices = data['close'].to_numpy()
            
            # Z-score anomalies
            z_anomalies, z_scores = StatisticalAnomalyDetector.z_score_anomaly(
                prices, self.config['z_score_threshold']
            )
            
            # IQR anomalies
            iqr_anomalies, iqr_scores = StatisticalAnomalyDetector.iqr_anomaly(
                prices, self.config['iqr_multiplier']
            )
            
            # Rolling anomalies
            rolling_anomalies, rolling_scores = StatisticalAnomalyDetector.rolling_anomaly(
                prices, self.config['rolling_window'], self.config['rolling_threshold']
            )
            
            # Create anomaly results
            for i, (z_anom, iqr_anom, roll_anom) in enumerate(zip(z_anomalies, iqr_anomalies, rolling_anomalies)):
                if z_anom or iqr_anom or roll_anom:
                    max_score = max(z_scores[i], iqr_scores[i], rolling_scores[i])
                    severity = self._calculate_severity(max_score)
                    
                    anomalies.append(AnomalyResult(
                        type=AnomalyType.STATISTICAL_OUTLIER,
                        severity=severity,
                        score=max_score,
                        description=f"Statistical outlier detected in {underlying} {timeframe}",
                        underlying=underlying,
                        timeframe=timeframe,
                        timestamp=datetime.now(),
                        metadata={
                            'z_score': z_scores[i],
                            'iqr_score': iqr_scores[i],
                            'rolling_score': rolling_scores[i],
                            'price': prices[i]
                        }
                    ))
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Error in statistical anomaly detection: {e}")
            return []
    
    async def _detect_volume_price_divergence(
        self, 
        data: pl.DataFrame, 
        underlying: str, 
        timeframe: str
    ) -> List[AnomalyResult]:
        """Detect volume-price divergence anomalies"""
        anomalies = []
        
        try:
            if 'volume' not in data.columns or data.height < 2:
                return anomalies
            
            # Calculate price and volume changes
            price_changes = data['close'].pct_change().drop_nulls().to_numpy()
            volume_changes = data['volume'].pct_change().drop_nulls().to_numpy()
            
            # Find divergences
            for i in range(len(price_changes)):
                price_change = abs(price_changes[i])
                volume_change = abs(volume_changes[i])
                
                # High volume with low price change
                if volume_change > 2.0 and price_change < self.config['volume_price_threshold']:
                    anomalies.append(AnomalyResult(
                        type=AnomalyType.VOLUME_PRICE_DIVERGENCE,
                        severity=AnomalySeverity.MEDIUM,
                        score=volume_change / max(price_change, 0.001),
                        description=f"Volume spike without price movement in {underlying} {timeframe}",
                        underlying=underlying,
                        timeframe=timeframe,
                        timestamp=datetime.now(),
                        metadata={
                            'volume_change': volume_change,
                            'price_change': price_change,
                            'divergence_ratio': volume_change / max(price_change, 0.001)
                        }
                    ))
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Error in volume-price divergence detection: {e}")
            return []
    
    async def _detect_cross_timeframe_anomalies(
        self, 
        data: pl.DataFrame, 
        underlying: str, 
        timeframe: str,
        cross_timeframe_data: Dict[str, pl.DataFrame]
    ) -> List[AnomalyResult]:
        """Detect cross-timeframe anomalies"""
        anomalies = []
        
        try:
            current_return = data['close'].pct_change().tail(1).item()
            
            for other_tf, other_data in cross_timeframe_data.items():
                if other_tf == timeframe or other_data.height == 0:
                    continue
                
                other_return = other_data['close'].pct_change().tail(1).item()
                
                # Check for significant divergence
                divergence = abs(current_return - other_return)
                if divergence > self.config['cross_timeframe_threshold']:
                    anomalies.append(AnomalyResult(
                        type=AnomalyType.CROSS_TIMEFRAME_DIVERGENCE,
                        severity=self._calculate_severity(divergence * 10),
                        score=divergence,
                        description=f"Cross-timeframe divergence between {timeframe} and {other_tf}",
                        underlying=underlying,
                        timeframe=timeframe,
                        timestamp=datetime.now(),
                        metadata={
                            'current_return': current_return,
                            'other_return': other_return,
                            'other_timeframe': other_tf,
                            'divergence': divergence
                        }
                    ))
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Error in cross-timeframe anomaly detection: {e}")
            return []
    
    async def _detect_ml_anomalies(
        self, 
        data: pl.DataFrame, 
        underlying: str, 
        timeframe: str
    ) -> List[AnomalyResult]:
        """Detect anomalies using machine learning"""
        anomalies = []
        
        try:
            if data.height < 50:  # Need sufficient data for ML
                return anomalies
            
            # Prepare features
            features = self._prepare_ml_features(data)
            if features is None:
                return anomalies
            
            # Fit model if not already fitted
            if not self.ml_detector.is_fitted:
                self.ml_detector.fit(features[:-10])  # Use most data for training
            
            # Predict anomalies on recent data
            recent_features = features[-10:]
            ml_anomalies, ml_scores = self.ml_detector.predict(recent_features)
            
            # Create anomaly results
            for i, (is_anomaly, score) in enumerate(zip(ml_anomalies, ml_scores)):
                if is_anomaly:
                    anomalies.append(AnomalyResult(
                        type=AnomalyType.ML_ANOMALY,
                        severity=self._calculate_severity(score),
                        score=score,
                        description=f"ML-detected anomaly in {underlying} {timeframe}",
                        underlying=underlying,
                        timeframe=timeframe,
                        timestamp=datetime.now(),
                        metadata={
                            'ml_score': score,
                            'feature_index': i
                        }
                    ))
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Error in ML anomaly detection: {e}")
            return []
    
    async def _detect_liquidity_anomalies(
        self, 
        data: pl.DataFrame, 
        underlying: str, 
        timeframe: str
    ) -> List[AnomalyResult]:
        """Detect liquidity anomalies"""
        anomalies = []
        
        try:
            if 'volume' not in data.columns or data.height < 10:
                return anomalies
            
            # Calculate average volume
            avg_volume = data['volume'].tail(20).mean()
            current_volume = data['volume'].tail(1).item()
            
            # Detect low liquidity
            if current_volume < avg_volume * 0.1:  # Less than 10% of average
                anomalies.append(AnomalyResult(
                    type=AnomalyType.LIQUIDITY_ANOMALY,
                    severity=AnomalySeverity.MEDIUM,
                    score=avg_volume / max(current_volume, 1),
                    description=f"Low liquidity detected in {underlying} {timeframe}",
                    underlying=underlying,
                    timeframe=timeframe,
                    timestamp=datetime.now(),
                    metadata={
                        'current_volume': current_volume,
                        'average_volume': avg_volume,
                        'volume_ratio': current_volume / avg_volume
                    }
                ))
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Error in liquidity anomaly detection: {e}")
            return []
    
    def _prepare_ml_features(self, data: pl.DataFrame) -> Optional[np.ndarray]:
        """Prepare features for ML anomaly detection"""
        try:
            features = []
            
            # Price features
            if 'close' in data.columns:
                prices = data['close'].to_numpy()
                features.extend([
                    prices,
                    np.diff(prices, prepend=prices[0]),  # Price changes
                    np.diff(prices, n=2, prepend=[prices[0], prices[1]])  # Second differences
                ])
            
            # Volume features
            if 'volume' in data.columns:
                volumes = data['volume'].to_numpy()
                features.extend([
                    volumes,
                    np.diff(volumes, prepend=volumes[0])  # Volume changes
                ])
            
            # OHLC features
            if all(col in data.columns for col in ['open', 'high', 'low', 'close']):
                ohlc = data.select(['open', 'high', 'low', 'close']).to_numpy()
                # Calculate ranges and ratios
                high_low_range = ohlc[:, 1] - ohlc[:, 2]  # High - Low
                open_close_range = ohlc[:, 3] - ohlc[:, 0]  # Close - Open
                features.extend([high_low_range, open_close_range])
            
            if not features:
                return None
            
            # Stack features and handle different lengths
            min_length = min(len(f) for f in features)
            features = [f[:min_length] for f in features]
            
            return np.column_stack(features)
            
        except Exception as e:
            logger.error(f"Error preparing ML features: {e}")
            return None
    
    def _calculate_severity(self, score: float) -> AnomalySeverity:
        """Calculate anomaly severity based on score"""
        if score < 2.0:
            return AnomalySeverity.LOW
        elif score < 4.0:
            return AnomalySeverity.MEDIUM
        elif score < 6.0:
            return AnomalySeverity.HIGH
        else:
            return AnomalySeverity.CRITICAL
    
    def get_anomaly_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get summary of recent anomalies"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_anomalies = [
            a for a in self.anomaly_history 
            if a.timestamp > cutoff_time
        ]
        
        summary = {
            'total_anomalies': len(recent_anomalies),
            'by_type': {},
            'by_severity': {},
            'by_underlying': {},
            'by_timeframe': {}
        }
        
        for anomaly in recent_anomalies:
            # By type
            type_key = anomaly.type.value
            summary['by_type'][type_key] = summary['by_type'].get(type_key, 0) + 1
            
            # By severity
            severity_key = anomaly.severity.name
            summary['by_severity'][severity_key] = summary['by_severity'].get(severity_key, 0) + 1
            
            # By underlying
            summary['by_underlying'][anomaly.underlying] = summary['by_underlying'].get(anomaly.underlying, 0) + 1
            
            # By timeframe
            summary['by_timeframe'][anomaly.timeframe] = summary['by_timeframe'].get(anomaly.timeframe, 0) + 1
        
        return summary
