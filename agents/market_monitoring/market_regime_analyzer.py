"""
Refined Market Regime Analysis Module

Provides advanced market regime analysis with multiple indicators, state-space models,
and volatility regime detection for comprehensive market environment assessment.
"""

import numpy as np
import polars as pl
import polars_talib as pt
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
from enum import Enum
import logging
from scipy import stats
from sklearn.mixture import GaussianMixture
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class TrendRegime(Enum):
    """Trend regime types"""
    STRONG_BULLISH = "strong_bullish"
    BULLISH = "bullish"
    SIDEWAYS = "sideways"
    BEARISH = "bearish"
    STRONG_BEARISH = "strong_bearish"


class MomentumRegime(Enum):
    """Momentum regime types"""
    STRONG_MOMENTUM = "strong_momentum"
    MOMENTUM = "momentum"
    NEUTRAL = "neutral"
    EXHAUSTION = "exhaustion"
    REVERSAL = "reversal"


class VolatilityRegime(Enum):
    """Volatility regime types"""
    LOW_VOL = "low_vol"
    NORMAL_VOL = "normal_vol"
    HIGH_VOL = "high_vol"
    EXTREME_VOL = "extreme_vol"


class LiquidityRegime(Enum):
    """Liquidity regime types"""
    HIGH_LIQUIDITY = "high_liquidity"
    NORMAL_LIQUIDITY = "normal_liquidity"
    LOW_LIQUIDITY = "low_liquidity"
    ILLIQUID = "illiquid"


@dataclass
class MarketRegimeState:
    """Complete market regime state"""
    trend: TrendRegime
    momentum: MomentumRegime
    volatility: VolatilityRegime
    liquidity: LiquidityRegime
    confidence: float
    timestamp: datetime
    underlying: str
    timeframe: str
    indicators: Dict[str, float]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'trend': self.trend.value,
            'momentum': self.momentum.value,
            'volatility': self.volatility.value,
            'liquidity': self.liquidity.value,
            'confidence': self.confidence,
            'timestamp': self.timestamp.isoformat(),
            'underlying': self.underlying,
            'timeframe': self.timeframe,
            'indicators': self.indicators
        }


class TechnicalIndicators:
    """Advanced technical indicators for regime analysis"""
    
    @staticmethod
    def calculate_adx(data: pl.DataFrame, period: int = 14) -> Optional[pl.DataFrame]:
        """Calculate Average Directional Index"""
        try:
            return data.with_columns([
                pt.adx(pl.col("high"), pl.col("low"), pl.col("close"), timeperiod=period).alias("adx"),
                pt.plus_di(pl.col("high"), pl.col("low"), pl.col("close"), timeperiod=period).alias("plus_di"),
                pt.minus_di(pl.col("high"), pl.col("low"), pl.col("close"), timeperiod=period).alias("minus_di")
            ])
        except Exception as e:
            logger.error(f"Error calculating ADX: {e}")
            return None
    
    @staticmethod
    def calculate_macd(data: pl.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> Optional[pl.DataFrame]:
        """Calculate MACD with signal and histogram"""
        try:
            return data.with_columns([
                pt.macd(pl.col("close"), fastperiod=fast, slowperiod=slow, signalperiod=signal).alias("macd"),
                pt.macdsignal(pl.col("close"), fastperiod=fast, slowperiod=slow, signalperiod=signal).alias("macd_signal"),
                pt.macdhist(pl.col("close"), fastperiod=fast, slowperiod=slow, signalperiod=signal).alias("macd_hist")
            ])
        except Exception as e:
            logger.error(f"Error calculating MACD: {e}")
            return None
    
    @staticmethod
    def calculate_stochastic(data: pl.DataFrame, k_period: int = 14, d_period: int = 3) -> Optional[pl.DataFrame]:
        """Calculate Stochastic Oscillator"""
        try:
            return data.with_columns([
                pt.stoch(pl.col("high"), pl.col("low"), pl.col("close"), 
                        fastk_period=k_period, slowk_period=d_period, slowd_period=d_period).alias("stoch_k"),
                pt.stoch(pl.col("high"), pl.col("low"), pl.col("close"), 
                        fastk_period=k_period, slowk_period=d_period, slowd_period=d_period).alias("stoch_d")
            ])
        except Exception as e:
            logger.error(f"Error calculating Stochastic: {e}")
            return None
    
    @staticmethod
    def calculate_atr(data: pl.DataFrame, period: int = 14) -> Optional[pl.DataFrame]:
        """Calculate Average True Range"""
        try:
            return data.with_columns([
                pt.atr(pl.col("high"), pl.col("low"), pl.col("close"), timeperiod=period).alias("atr")
            ])
        except Exception as e:
            logger.error(f"Error calculating ATR: {e}")
            return None
    
    @staticmethod
    def calculate_bollinger_bands(data: pl.DataFrame, period: int = 20, std_dev: float = 2.0) -> Optional[pl.DataFrame]:
        """Calculate Bollinger Bands"""
        try:
            return data.with_columns([
                pt.bbands(pl.col("close"), timeperiod=period, nbdevup=std_dev, nbdevdn=std_dev).alias("bb_upper"),
                pt.bbands(pl.col("close"), timeperiod=period, nbdevup=std_dev, nbdevdn=std_dev).alias("bb_middle"),
                pt.bbands(pl.col("close"), timeperiod=period, nbdevup=std_dev, nbdevdn=std_dev).alias("bb_lower")
            ])
        except Exception as e:
            logger.error(f"Error calculating Bollinger Bands: {e}")
            return None
    
    @staticmethod
    def calculate_rsi(data: pl.DataFrame, period: int = 14) -> Optional[pl.DataFrame]:
        """Calculate RSI"""
        try:
            return data.with_columns([
                pt.rsi(pl.col("close"), timeperiod=period).alias("rsi")
            ])
        except Exception as e:
            logger.error(f"Error calculating RSI: {e}")
            return None
    
    @staticmethod
    def calculate_sma(data: pl.DataFrame, period: int = 20) -> Optional[pl.DataFrame]:
        """Calculate Simple Moving Average"""
        try:
            return data.with_columns([
                pt.sma(pl.col("close"), timeperiod=period).alias(f"sma_{period}")
            ])
        except Exception as e:
            logger.error(f"Error calculating SMA: {e}")
            return None


class RegimeClassifier:
    """Machine learning-based regime classification"""
    
    def __init__(self, n_components: int = 3):
        self.n_components = n_components
        self.gmm = GaussianMixture(n_components=n_components, random_state=42)
        self.scaler = StandardScaler()
        self.is_fitted = False
        self.regime_labels = {}
    
    def fit(self, features: np.ndarray, regime_names: Optional[List[str]] = None) -> bool:
        """Fit the regime classification model"""
        try:
            if features.shape[0] < self.n_components * 10:  # Need sufficient samples
                return False
            
            # Scale features
            features_scaled = self.scaler.fit_transform(features)
            
            # Fit Gaussian Mixture Model
            self.gmm.fit(features_scaled)
            
            # Assign regime labels
            if regime_names and len(regime_names) == self.n_components:
                self.regime_labels = {i: name for i, name in enumerate(regime_names)}
            else:
                self.regime_labels = {i: f"regime_{i}" for i in range(self.n_components)}
            
            self.is_fitted = True
            return True
        except Exception as e:
            logger.error(f"Failed to fit regime classifier: {e}")
            return False
    
    def predict(self, features: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Predict regime probabilities"""
        if not self.is_fitted:
            return np.zeros(features.shape[0]), np.zeros((features.shape[0], self.n_components))
        
        try:
            features_scaled = self.scaler.transform(features)
            predictions = self.gmm.predict(features_scaled)
            probabilities = self.gmm.predict_proba(features_scaled)
            
            return predictions, probabilities
        except Exception as e:
            logger.error(f"Failed to predict regimes: {e}")
            return np.zeros(features.shape[0]), np.zeros((features.shape[0], self.n_components))


class MarketRegimeAnalyzer:
    """
    Advanced market regime analysis system.
    
    Features:
    - Multiple technical indicators (ADX, MACD, Stochastic, RSI, ATR, Bollinger Bands)
    - Machine learning-based regime classification
    - Volatility regime detection
    - Liquidity regime analysis
    - Confidence scoring
    - Historical regime tracking
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or self._get_default_config()
        self.regime_classifier = RegimeClassifier(n_components=3)
        self.regime_history: Dict[str, List[MarketRegimeState]] = {}
        self.max_history_size = 1000
        
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'adx_period': 14,
            'adx_trend_threshold': 25,
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            'rsi_period': 14,
            'rsi_overbought': 70,
            'rsi_oversold': 30,
            'atr_period': 14,
            'bb_period': 20,
            'bb_std': 2.0,
            'stoch_k': 14,
            'stoch_d': 3,
            'vol_percentiles': [25, 50, 75, 90],
            'min_data_points': 50
        }

    async def analyze_market_regime(
        self,
        data: pl.DataFrame,
        underlying: str,
        timeframe: str
    ) -> Optional[MarketRegimeState]:
        """
        Analyze market regime for given data.

        Args:
            data: Market data DataFrame
            underlying: Underlying symbol
            timeframe: Timeframe

        Returns:
            MarketRegimeState or None if insufficient data
        """
        if data.height < self.config['min_data_points']:
            logger.debug(f"Insufficient data for regime analysis: {data.height} < {self.config['min_data_points']}")
            return None

        try:
            # Calculate all technical indicators
            indicators_data = await self._calculate_all_indicators(data)
            if indicators_data is None:
                return None

            # Extract latest indicator values
            latest_indicators = self._extract_latest_indicators(indicators_data)

            # Analyze each regime component
            trend_regime = self._analyze_trend_regime(latest_indicators)
            momentum_regime = self._analyze_momentum_regime(latest_indicators)
            volatility_regime = self._analyze_volatility_regime(indicators_data)
            liquidity_regime = self._analyze_liquidity_regime(data)

            # Calculate overall confidence
            confidence = self._calculate_confidence(latest_indicators)

            # Create regime state
            regime_state = MarketRegimeState(
                trend=trend_regime,
                momentum=momentum_regime,
                volatility=volatility_regime,
                liquidity=liquidity_regime,
                confidence=confidence,
                timestamp=datetime.now(),
                underlying=underlying,
                timeframe=timeframe,
                indicators=latest_indicators
            )

            # Store in history
            if underlying not in self.regime_history:
                self.regime_history[underlying] = []

            self.regime_history[underlying].append(regime_state)
            if len(self.regime_history[underlying]) > self.max_history_size:
                self.regime_history[underlying] = self.regime_history[underlying][-self.max_history_size:]

            return regime_state

        except Exception as e:
            logger.error(f"Error analyzing market regime for {underlying} {timeframe}: {e}")
            return None

    async def _calculate_all_indicators(self, data: pl.DataFrame) -> Optional[pl.DataFrame]:
        """Calculate all technical indicators"""
        try:
            result = data.clone()

            # Trend indicators
            adx_data = TechnicalIndicators.calculate_adx(result, self.config['adx_period'])
            if adx_data is not None:
                result = adx_data

            macd_data = TechnicalIndicators.calculate_macd(
                result,
                self.config['macd_fast'],
                self.config['macd_slow'],
                self.config['macd_signal']
            )
            if macd_data is not None:
                result = macd_data

            # Momentum indicators
            rsi_data = TechnicalIndicators.calculate_rsi(result, self.config['rsi_period'])
            if rsi_data is not None:
                result = rsi_data

            stoch_data = TechnicalIndicators.calculate_stochastic(
                result,
                self.config['stoch_k'],
                self.config['stoch_d']
            )
            if stoch_data is not None:
                result = stoch_data

            # Volatility indicators
            atr_data = TechnicalIndicators.calculate_atr(result, self.config['atr_period'])
            if atr_data is not None:
                result = atr_data

            bb_data = TechnicalIndicators.calculate_bollinger_bands(
                result,
                self.config['bb_period'],
                self.config['bb_std']
            )
            if bb_data is not None:
                result = bb_data

            # Moving averages
            sma_data = TechnicalIndicators.calculate_sma(result, 20)
            if sma_data is not None:
                result = sma_data

            return result

        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            return None

    def _extract_latest_indicators(self, data: pl.DataFrame) -> Dict[str, float]:
        """Extract latest indicator values"""
        indicators = {}

        try:
            latest_row = data.tail(1)

            # Get all numeric columns
            for col in data.columns:
                if col not in ['timestamp', 'symbol'] and data[col].dtype in [pl.Float64, pl.Float32, pl.Int64, pl.Int32]:
                    try:
                        value = latest_row[col].item()
                        if value is not None and not np.isnan(value):
                            indicators[col] = float(value)
                    except:
                        continue

            return indicators

        except Exception as e:
            logger.error(f"Error extracting indicators: {e}")
            return {}

    def _analyze_trend_regime(self, indicators: Dict[str, float]) -> TrendRegime:
        """Analyze trend regime using multiple indicators"""
        try:
            trend_signals = []

            # ADX-based trend analysis
            if 'adx' in indicators and 'plus_di' in indicators and 'minus_di' in indicators:
                adx = indicators['adx']
                plus_di = indicators['plus_di']
                minus_di = indicators['minus_di']

                if adx > self.config['adx_trend_threshold']:
                    if plus_di > minus_di:
                        trend_signals.append(2 if adx > 40 else 1)  # Strong bullish or bullish
                    else:
                        trend_signals.append(-2 if adx > 40 else -1)  # Strong bearish or bearish
                else:
                    trend_signals.append(0)  # Sideways

            # MACD-based trend analysis
            if 'macd' in indicators and 'macd_signal' in indicators:
                macd = indicators['macd']
                macd_signal = indicators['macd_signal']

                if macd > macd_signal:
                    trend_signals.append(1)  # Bullish
                elif macd < macd_signal:
                    trend_signals.append(-1)  # Bearish
                else:
                    trend_signals.append(0)  # Neutral

            # SMA-based trend analysis
            if 'close' in indicators and 'sma_20' in indicators:
                close = indicators['close']
                sma = indicators['sma_20']

                price_vs_sma = (close - sma) / sma
                if price_vs_sma > 0.02:
                    trend_signals.append(1)  # Bullish
                elif price_vs_sma < -0.02:
                    trend_signals.append(-1)  # Bearish
                else:
                    trend_signals.append(0)  # Sideways

            # Aggregate trend signals
            if not trend_signals:
                return TrendRegime.SIDEWAYS

            avg_signal = np.mean(trend_signals)

            if avg_signal >= 1.5:
                return TrendRegime.STRONG_BULLISH
            elif avg_signal >= 0.5:
                return TrendRegime.BULLISH
            elif avg_signal <= -1.5:
                return TrendRegime.STRONG_BEARISH
            elif avg_signal <= -0.5:
                return TrendRegime.BEARISH
            else:
                return TrendRegime.SIDEWAYS

        except Exception as e:
            logger.error(f"Error analyzing trend regime: {e}")
            return TrendRegime.SIDEWAYS

    def _analyze_momentum_regime(self, indicators: Dict[str, float]) -> MomentumRegime:
        """Analyze momentum regime using multiple indicators"""
        try:
            momentum_signals = []

            # RSI-based momentum analysis
            if 'rsi' in indicators:
                rsi = indicators['rsi']

                if rsi > 80:
                    momentum_signals.append(-2)  # Extreme overbought (reversal)
                elif rsi > self.config['rsi_overbought']:
                    momentum_signals.append(-1)  # Overbought (exhaustion)
                elif rsi < 20:
                    momentum_signals.append(2)  # Extreme oversold (strong momentum up)
                elif rsi < self.config['rsi_oversold']:
                    momentum_signals.append(1)  # Oversold (momentum)
                else:
                    momentum_signals.append(0)  # Neutral

            # Stochastic-based momentum analysis
            if 'stoch_k' in indicators and 'stoch_d' in indicators:
                stoch_k = indicators['stoch_k']
                stoch_d = indicators['stoch_d']

                if stoch_k > 80 and stoch_d > 80:
                    momentum_signals.append(-1)  # Overbought
                elif stoch_k < 20 and stoch_d < 20:
                    momentum_signals.append(1)  # Oversold
                elif stoch_k > stoch_d:
                    momentum_signals.append(0.5)  # Bullish momentum
                elif stoch_k < stoch_d:
                    momentum_signals.append(-0.5)  # Bearish momentum
                else:
                    momentum_signals.append(0)  # Neutral

            # MACD histogram-based momentum
            if 'macd_hist' in indicators:
                macd_hist = indicators['macd_hist']

                if abs(macd_hist) > 0.5:  # Strong momentum
                    momentum_signals.append(2 if macd_hist > 0 else -2)
                elif abs(macd_hist) > 0.1:  # Moderate momentum
                    momentum_signals.append(1 if macd_hist > 0 else -1)
                else:
                    momentum_signals.append(0)  # Neutral

            # Aggregate momentum signals
            if not momentum_signals:
                return MomentumRegime.NEUTRAL

            avg_signal = np.mean(momentum_signals)

            if avg_signal >= 1.5:
                return MomentumRegime.STRONG_MOMENTUM
            elif avg_signal >= 0.5:
                return MomentumRegime.MOMENTUM
            elif avg_signal <= -1.5:
                return MomentumRegime.REVERSAL
            elif avg_signal <= -0.5:
                return MomentumRegime.EXHAUSTION
            else:
                return MomentumRegime.NEUTRAL

        except Exception as e:
            logger.error(f"Error analyzing momentum regime: {e}")
            return MomentumRegime.NEUTRAL

    def _analyze_volatility_regime(self, data: pl.DataFrame) -> VolatilityRegime:
        """Analyze volatility regime using ATR and Bollinger Bands"""
        try:
            volatility_signals = []

            # ATR-based volatility analysis
            if 'atr' in data.columns and data.height >= 20:
                atr_values = data['atr'].tail(20).to_numpy()
                current_atr = atr_values[-1]
                atr_percentiles = np.percentile(atr_values, self.config['vol_percentiles'])

                if current_atr > atr_percentiles[3]:  # Above 90th percentile
                    volatility_signals.append(3)  # Extreme volatility
                elif current_atr > atr_percentiles[2]:  # Above 75th percentile
                    volatility_signals.append(2)  # High volatility
                elif current_atr > atr_percentiles[1]:  # Above 50th percentile
                    volatility_signals.append(1)  # Normal volatility
                else:
                    volatility_signals.append(0)  # Low volatility

            # Bollinger Bands width analysis
            if all(col in data.columns for col in ['bb_upper', 'bb_lower', 'bb_middle']) and data.height >= 20:
                bb_data = data.tail(20)
                bb_width = (bb_data['bb_upper'] - bb_data['bb_lower']) / bb_data['bb_middle']
                current_width = bb_width.tail(1).item()
                width_percentiles = np.percentile(bb_width.to_numpy(), self.config['vol_percentiles'])

                if current_width > width_percentiles[3]:
                    volatility_signals.append(3)  # Extreme volatility
                elif current_width > width_percentiles[2]:
                    volatility_signals.append(2)  # High volatility
                elif current_width > width_percentiles[1]:
                    volatility_signals.append(1)  # Normal volatility
                else:
                    volatility_signals.append(0)  # Low volatility

            # Price volatility analysis
            if 'close' in data.columns and data.height >= 20:
                returns = data['close'].pct_change().tail(20).drop_nulls().to_numpy()
                current_vol = np.std(returns) * np.sqrt(252)  # Annualized volatility

                if current_vol > 0.4:  # 40% annualized
                    volatility_signals.append(3)
                elif current_vol > 0.25:  # 25% annualized
                    volatility_signals.append(2)
                elif current_vol > 0.15:  # 15% annualized
                    volatility_signals.append(1)
                else:
                    volatility_signals.append(0)

            # Aggregate volatility signals
            if not volatility_signals:
                return VolatilityRegime.NORMAL_VOL

            avg_signal = np.mean(volatility_signals)

            if avg_signal >= 2.5:
                return VolatilityRegime.EXTREME_VOL
            elif avg_signal >= 1.5:
                return VolatilityRegime.HIGH_VOL
            elif avg_signal >= 0.5:
                return VolatilityRegime.NORMAL_VOL
            else:
                return VolatilityRegime.LOW_VOL

        except Exception as e:
            logger.error(f"Error analyzing volatility regime: {e}")
            return VolatilityRegime.NORMAL_VOL

    def _analyze_liquidity_regime(self, data: pl.DataFrame) -> LiquidityRegime:
        """Analyze liquidity regime based on volume patterns"""
        try:
            if 'volume' not in data.columns or data.height < 20:
                return LiquidityRegime.NORMAL_LIQUIDITY

            # Volume analysis
            volume_data = data['volume'].tail(20)
            current_volume = volume_data.tail(1).item()
            avg_volume = volume_data.mean()
            volume_std = volume_data.std()

            # Volume consistency (lower std relative to mean indicates better liquidity)
            volume_cv = volume_std / avg_volume if avg_volume > 0 else float('inf')

            # Volume level relative to average
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0

            # Combine signals
            if volume_ratio > 2.0 and volume_cv < 0.5:
                return LiquidityRegime.HIGH_LIQUIDITY
            elif volume_ratio > 0.5 and volume_cv < 1.0:
                return LiquidityRegime.NORMAL_LIQUIDITY
            elif volume_ratio > 0.1 and volume_cv < 2.0:
                return LiquidityRegime.LOW_LIQUIDITY
            else:
                return LiquidityRegime.ILLIQUID

        except Exception as e:
            logger.error(f"Error analyzing liquidity regime: {e}")
            return LiquidityRegime.NORMAL_LIQUIDITY

    def _calculate_confidence(self, indicators: Dict[str, float]) -> float:
        """Calculate confidence score for regime analysis"""
        try:
            confidence_factors = []

            # Data completeness
            expected_indicators = ['adx', 'macd', 'rsi', 'atr', 'close']
            available_indicators = sum(1 for ind in expected_indicators if ind in indicators)
            data_completeness = available_indicators / len(expected_indicators)
            confidence_factors.append(data_completeness)

            # Signal strength
            if 'adx' in indicators:
                adx_strength = min(indicators['adx'] / 50, 1.0)  # Normalize to 0-1
                confidence_factors.append(adx_strength)

            if 'atr' in indicators and 'close' in indicators:
                atr_relative = indicators['atr'] / indicators['close']
                atr_confidence = 1.0 - min(atr_relative * 10, 1.0)  # Lower ATR = higher confidence
                confidence_factors.append(atr_confidence)

            # Overall confidence
            return np.mean(confidence_factors) if confidence_factors else 0.5

        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return 0.5

    def get_regime_history(self, underlying: str, hours: int = 24) -> List[MarketRegimeState]:
        """Get regime history for an underlying"""
        if underlying not in self.regime_history:
            return []

        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [
            regime for regime in self.regime_history[underlying]
            if regime.timestamp > cutoff_time
        ]

    def get_regime_summary(self, underlying: str, hours: int = 24) -> Dict[str, Any]:
        """Get regime analysis summary"""
        history = self.get_regime_history(underlying, hours)

        if not history:
            return {'error': 'No regime history available'}

        latest = history[-1]

        # Calculate regime stability
        trend_changes = sum(1 for i in range(1, len(history))
                          if history[i].trend != history[i-1].trend)

        return {
            'latest_regime': latest.to_dict(),
            'regime_stability': 1.0 - (trend_changes / max(len(history), 1)),
            'avg_confidence': np.mean([r.confidence for r in history]),
            'total_observations': len(history),
            'trend_distribution': self._get_regime_distribution(history, 'trend'),
            'volatility_distribution': self._get_regime_distribution(history, 'volatility')
        }

    def _get_regime_distribution(self, history: List[MarketRegimeState], regime_type: str) -> Dict[str, float]:
        """Get distribution of regime types"""
        if not history:
            return {}

        regime_counts = {}
        for regime_state in history:
            regime_value = getattr(regime_state, regime_type).value
            regime_counts[regime_value] = regime_counts.get(regime_value, 0) + 1

        total = len(history)
        return {k: v / total for k, v in regime_counts.items()}
