Metadata-Version: 2.2
Name: python-json-logger
Version: 3.3.0
Summary: JSON Log Formatter for the Python Logging Package
Author-email: <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>
Maintainer-email: <PERSON> <<EMAIL>>
License: BSD-2-Clause License
Project-URL: Homepage, https://nhairs.github.io/python-json-logger
Project-URL: GitHub, https://github.com/nhairs/python-json-logger
Classifier: Development Status :: 6 - Mature
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: System :: Logging
Classifier: Typing :: Typed
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
License-File: NOTICE
Requires-Dist: typing_extensions; python_version < "3.10"
Provides-Extra: dev
Requires-Dist: orjson; implementation_name != "pypy" and extra == "dev"
Requires-Dist: msgspec; implementation_name != "pypy" and extra == "dev"
Requires-Dist: validate-pyproject[all]; extra == "dev"
Requires-Dist: black; extra == "dev"
Requires-Dist: pylint; extra == "dev"
Requires-Dist: mypy; extra == "dev"
Requires-Dist: pytest; extra == "dev"
Requires-Dist: freezegun; extra == "dev"
Requires-Dist: backports.zoneinfo; python_version < "3.9" and extra == "dev"
Requires-Dist: tzdata; extra == "dev"
Requires-Dist: build; extra == "dev"
Requires-Dist: mkdocs; extra == "dev"
Requires-Dist: mkdocs-material>=8.5; extra == "dev"
Requires-Dist: mkdocs-awesome-pages-plugin; extra == "dev"
Requires-Dist: mdx_truly_sane_lists; extra == "dev"
Requires-Dist: mkdocstrings[python]; extra == "dev"
Requires-Dist: mkdocs-gen-files; extra == "dev"
Requires-Dist: mkdocs-literate-nav; extra == "dev"
Requires-Dist: mike; extra == "dev"

[![PyPi](https://img.shields.io/pypi/v/python-json-logger.svg)](https://pypi.python.org/pypi/python-json-logger/)
[![PyPI - Status](https://img.shields.io/pypi/status/python-json-logger)](https://pypi.python.org/pypi/python-json-logger/)
[![PyPI - Downloads](https://img.shields.io/pypi/dm/python-json-logger)](https://pypi.python.org/pypi/python-json-logger/)
[![Python Versions](https://img.shields.io/pypi/pyversions/python-json-logger.svg)](https://github.com/nhairs/python-json-logger)
[![License](https://img.shields.io/github/license/nhairs/python-json-logger.svg)](https://github.com/nhairs/python-json-logger)
![Build Status](https://github.com/nhairs/python-json-logger/actions/workflows/test-suite.yml/badge.svg)
#
# Python JSON Logger

Python JSON Logger enables you produce JSON logs when using Python's `logging` package.

JSON logs are machine readable allowing for much easier parsing and ingestion into log aggregation tools.


## Documentation

- [Documentation](https://nhairs.github.io/python-json-logger/latest/)
- [Quickstart Guide](https://nhairs.github.io/python-json-logger/latest/quickstart/)
- [Change Log](https://nhairs.github.io/python-json-logger/latest/changelog/)
- [Contributing](https://nhairs.github.io/python-json-logger/latest/contributing/)

## License

This project is licensed under the BSD 2 Clause License - see [`LICENSE`](https://github.com/nhairs/python-json-logger/blob/main/LICENSE)

## Authors and Maintainers

This project was originally authored by [Zakaria Zajac](https://github.com/madzak) and our wonderful [contributors](https://github.com/nhairs/python-json-logger/graphs/contributors)

It is currently maintained by:

- [Nicholas Hairs](https://github.com/nhairs) - [nicholashairs.com](https://www.nicholashairs.com)
